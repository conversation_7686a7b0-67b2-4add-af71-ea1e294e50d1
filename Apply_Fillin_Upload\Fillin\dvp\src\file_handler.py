import os
import glob
from src.logger_setup import setup_logger

logger = setup_logger()

def find_target_file(input_dir: str = "input") -> str | None:
    """扫描输入文件夹查找包含'软件设计验证计划'的 .xlsx 文件"""
    files = glob.glob(os.path.join(input_dir, "*.xlsx"))
    for file in files:
        if "软件设计验证计划" in os.path.basename(file):
            logger.info(f"找到目标文件: {file}")
            return file
    logger.error(f"在 {input_dir} 文件夹未找到包含 '软件设计验证计划' 的 .xlsx 文件")
    return None

def get_output_path(input_path: str, output_dir: str = "output") -> str:
    """生成输出文件路径，保持原文件名，放置在输出文件夹"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    filename = os.path.basename(input_path)
    return os.path.join(output_dir, filename)