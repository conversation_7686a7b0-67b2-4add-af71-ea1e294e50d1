#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VSETools 1.0 测试脚本
用于验证主GUI是否能正常启动和运行
"""

import sys
import os
import traceback

def test_imports():
    """测试导入模块"""
    print("正在测试模块导入...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5.QtWidgets 导入成功")
    except ImportError as e:
        print(f"✗ PyQt5.QtWidgets 导入失败: {e}")
        return False
    
    try:
        from PyQt5.QtCore import Qt
        print("✓ PyQt5.QtCore 导入成功")
    except ImportError as e:
        print(f"✗ PyQt5.QtCore 导入失败: {e}")
        return False
    
    try:
        from PyQt5.QtGui import QFont
        print("✓ PyQt5.QtGui 导入成功")
    except ImportError as e:
        print(f"✗ PyQt5.QtGui 导入失败: {e}")
        return False
    
    return True

def test_main_gui():
    """测试主GUI"""
    print("\n正在测试主GUI...")
    
    try:
        # 设置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 导入主GUI
        sys.path.insert(0, current_dir)
        from main_gui import MainWindow
        print("✓ 主GUI模块导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = MainWindow()
        print("✓ 主窗口创建成功")
        
        # 显示窗口（但不运行事件循环）
        window.show()
        print("✓ 主窗口显示成功")
        
        # 立即关闭窗口
        window.close()
        print("✓ 主窗口关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 主GUI测试失败: {e}")
        traceback.print_exc()
        return False

def test_autocali_gui():
    """测试AutoCali GUI"""
    print("\n正在测试AutoCali GUI...")
    
    try:
        # 设置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        autocali_dir = os.path.join(current_dir, 'AutoCali')
        
        if not os.path.exists(autocali_dir):
            print("✗ AutoCali目录不存在")
            return False
        
        sys.path.insert(0, autocali_dir)
        from AutoCali_GUI import AutoCaliGUI
        print("✓ AutoCali GUI模块导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建AutoCali窗口
        window = AutoCaliGUI()
        print("✓ AutoCali窗口创建成功")
        
        # 显示窗口（但不运行事件循环）
        window.show()
        print("✓ AutoCali窗口显示成功")
        
        # 立即关闭窗口
        window.close()
        print("✓ AutoCali窗口关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ AutoCali GUI测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("VSETools 1.0 测试脚本")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查PyQt5安装")
        return False
    
    # 测试主GUI
    if not test_main_gui():
        print("\n❌ 主GUI测试失败")
        return False
    
    # 测试AutoCali GUI
    if not test_autocali_gui():
        print("\n⚠️  AutoCali GUI测试失败（可能是正常的）")
    
    print("\n" + "=" * 50)
    print("🎉 基础测试完成！")
    print("如果所有测试都通过，可以尝试运行主程序：")
    print("python main_gui.py")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    main()
