import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QPushButton, QStackedWidget, QFrame,
    QMessageBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_styles()
        
    def init_ui(self):
        self.setWindowTitle("VSETools 1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央控件和堆叠布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建堆叠控件来管理不同页面
        self.stacked_widget = QStackedWidget()
        
        # 创建主页面
        self.home_page = self.create_home_page()
        self.stacked_widget.addWidget(self.home_page)
        
        # 创建自动软件开发页面
        self.auto_dev_page = self.create_auto_dev_page()
        self.stacked_widget.addWidget(self.auto_dev_page)
        
        # 设置布局
        layout = QVBoxLayout()
        layout.addWidget(self.stacked_widget)
        central_widget.setLayout(layout)
        
        # 设置当前页面为主页
        self.stacked_widget.setCurrentWidget(self.home_page)
        
    def create_home_page(self):
        """创建主页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        
        # 添加顶部空白
        layout.addStretch(1)
        
        # 创建标题
        title_label = QLabel("VSETools 1.0")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 22, QFont.Bold))  # 22pt
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 40px;")
        layout.addWidget(title_label)
        
        # 创建按钮容器
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setSpacing(50)
        
        # 创建自动文件处理按钮
        file_process_btn = QPushButton("自动文件处理")
        file_process_btn.setFont(QFont("Arial", 13, QFont.Bold))  # 13pt
        file_process_btn.setMinimumSize(200, 80)
        file_process_btn.clicked.connect(self.open_file_process)
        file_process_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 20px;
                font-size: 13pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        # 创建自动软件开发按钮
        auto_dev_btn = QPushButton("自动软件开发")
        auto_dev_btn.setFont(QFont("Arial", 13, QFont.Bold))  # 13pt
        auto_dev_btn.setMinimumSize(200, 80)
        auto_dev_btn.clicked.connect(self.open_auto_dev)
        auto_dev_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 20px;
                font-size: 13pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        
        button_layout.addWidget(file_process_btn)
        button_layout.addWidget(auto_dev_btn)
        
        # 居中按钮容器
        layout.addWidget(button_container, alignment=Qt.AlignCenter)
        
        # 添加底部空白
        layout.addStretch(1)
        
        return page
    
    def create_auto_dev_page(self):
        """创建自动软件开发页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        
        # 创建标题
        title_label = QLabel("自动软件开发")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))  # 16pt
        title_label.setStyleSheet("color: #2c3e50; margin: 20px 0;")
        layout.addWidget(title_label)
        
        # 创建按钮网格
        button_container = QWidget()
        button_layout = QGridLayout(button_container)
        button_layout.setSpacing(30)
        
        # 创建功能按钮
        buttons = [
            ("软件标定", self.open_auto_cali, "#e74c3c"),
            ("软件集成", self.open_software_integration, "#9b59b6"),
            ("DM制作", self.open_dm_utils, "#f39c12"),
            ("RTE点检", self.open_rte_test, "#1abc9c")
        ]
        
        for i, (text, handler, color) in enumerate(buttons):
            btn = QPushButton(text)
            btn.setFont(QFont("Arial", 12, QFont.Bold))  # 12pt
            btn.setMinimumSize(180, 100)
            btn.clicked.connect(handler)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 20px;
                    font-size: 12pt;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, 0.8)};
                }}
            """)
            
            row = i // 2
            col = i % 2
            button_layout.addWidget(btn, row, col)
        
        layout.addWidget(button_container, alignment=Qt.AlignCenter)
        
        # 添加返回主页按钮
        back_btn = QPushButton("返回主页")
        back_btn.setFont(QFont("Arial", 11))  # 11pt
        back_btn.setMinimumSize(120, 40)
        back_btn.clicked.connect(self.back_to_home)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-size: 11pt;
                margin-top: 30px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        layout.addWidget(back_btn, alignment=Qt.AlignCenter)
        
        return page
    
    def setup_styles(self):
        """设置整体样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                background-color: #ecf0f1;
            }
        """)
    
    def darken_color(self, color, factor=0.85):
        """使颜色变暗"""
        color = color.lstrip('#')
        r, g, b = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def back_to_home(self):
        """返回主页"""
        self.stacked_widget.setCurrentWidget(self.home_page)
    
    def open_file_process(self):
        """打开自动文件处理功能"""
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), 'Apply_Fillin_Upload'))
            from main_gui_final import VehicleManagementGUI
            if not hasattr(self, 'file_process_page'):
                self.file_process_page = VehicleManagementGUI(on_home=self.back_to_home)
                self.stacked_widget.addWidget(self.file_process_page)
            self.stacked_widget.setCurrentWidget(self.file_process_page)
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载文件处理模块: {str(e)}")
    
    def open_auto_dev(self):
        """打开自动软件开发页面"""
        self.stacked_widget.setCurrentWidget(self.auto_dev_page)
    
    def open_auto_cali(self):
        """打开自动标定功能"""
        try:
            # 导入AutoCali模块
            sys.path.append(os.path.join(os.path.dirname(__file__), 'AutoCali'))
            # 创建AutoCali页面
            if not hasattr(self, 'auto_cali_page'):
                from AutoCali_GUI import AutoCaliGUI
                self.auto_cali_page = AutoCaliGUI(on_home=self.back_to_home, on_back=self.open_auto_dev)
                self.stacked_widget.addWidget(self.auto_cali_page)
            self.stacked_widget.setCurrentWidget(self.auto_cali_page)
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载自动标定模块: {str(e)}")
    
    def open_software_integration(self):
        """打开软件集成功能"""
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), 'Integration'))
            from Integration_GUI import IntegrationGUI
            if not hasattr(self, 'integration_page'):
                self.integration_page = IntegrationGUI(on_home=self.back_to_home, on_back=self.open_auto_dev)
                self.stacked_widget.addWidget(self.integration_page)
            self.stacked_widget.setCurrentWidget(self.integration_page)
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载软件集成模块: {str(e)}")
    
    def open_dm_utils(self):
        """打开DM制作功能"""
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), 'DMUtils'))
            from DMUtils_GUI import DMUtilsGUI
            if not hasattr(self, 'dm_utils_page'):
                self.dm_utils_page = DMUtilsGUI(on_home=self.back_to_home, on_back=self.open_auto_dev)
                self.stacked_widget.addWidget(self.dm_utils_page)
            self.stacked_widget.setCurrentWidget(self.dm_utils_page)
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载DM制作模块: {str(e)}")
    
    def open_rte_test(self):
        """打开RTE点检功能"""
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), 'RTETestUtils'))
            from RTE_GUI import RTEGUI
            if not hasattr(self, 'rte_page'):
                self.rte_page = RTEGUI(on_home=self.back_to_home, on_back=self.open_auto_dev)
                self.stacked_widget.addWidget(self.rte_page)
            self.stacked_widget.setCurrentWidget(self.rte_page)
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载RTE点检模块: {str(e)}")
    
    def add_home_button_to_widget(self, widget):
        """为指定控件添加返回主页按钮，字号11pt"""
        if hasattr(widget, 'layout') and widget.layout():
            home_btn = QPushButton("返回主页")
            home_btn.setFont(QFont("Arial", 11))
            home_btn.setMaximumSize(120, 36)
            home_btn.clicked.connect(self.back_to_home)
            home_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                    font-size: 11pt;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            widget.layout().insertWidget(0, home_btn)
    
    def add_nav_buttons_to_widget(self, widget):
        """为指定控件添加导航按钮，字号11pt"""
        if hasattr(widget, 'layout') and widget.layout():
            nav_container = QWidget()
            nav_layout = QHBoxLayout(nav_container)
            nav_layout.setContentsMargins(0, 0, 0, 10)
            
            # 返回上一级按钮
            back_btn = QPushButton("返回上一级")
            back_btn.setFont(QFont("Arial", 11))
            back_btn.setMaximumSize(120, 36)
            back_btn.clicked.connect(self.open_auto_dev)
            back_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                    font-size: 11pt;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
            """)
            
            # 返回主页按钮
            home_btn = QPushButton("返回主页")
            home_btn.setFont(QFont("Arial", 11))
            home_btn.setMaximumSize(120, 36)
            home_btn.clicked.connect(self.back_to_home)
            home_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                    font-size: 11pt;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            nav_layout.addWidget(back_btn)
            nav_layout.addWidget(home_btn)
            nav_layout.addStretch()
            
            # 将导航容器添加到控件的布局顶部
            widget.layout().insertWidget(0, nav_container)


def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
