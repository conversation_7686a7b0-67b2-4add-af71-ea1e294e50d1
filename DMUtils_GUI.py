import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFileDialog, QLineEdit, QGroupBox, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class DMUtilsGUI(QMainWindow):
    def __init__(self, on_home=None, on_back=None):
        super().__init__()
        self.on_home = on_home
        self.on_back = on_back
        self.setWindowTitle("DM制作工具")
        self.setGeometry(220, 140, 700, 450)
        self.init_ui()
        self.setup_styles()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 10, 20, 20)
        
        # 顶部导航
        nav_container = self.create_nav_bar()
        main_layout.addWidget(nav_container)
        
        # 标题
        title_label = QLabel("DM制作工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0 20px 0;")
        main_layout.addWidget(title_label)
        
        # 配置区域
        config_group = self.create_config_group()
        main_layout.addWidget(config_group)
        
        # 文件夹区域
        folder_group = self.create_folder_group()
        main_layout.addWidget(folder_group)
        
        # 操作按钮区域
        button_layout = self.create_button_layout()
        main_layout.addLayout(button_layout)
        
        # 状态提示
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("Arial", 11))
        self.status_label.setStyleSheet("color: #27ae60; margin: 10px 0; padding: 5px;")
        main_layout.addWidget(self.status_label)
        
        main_layout.addStretch()

    def create_nav_bar(self):
        """创建导航栏"""
        nav_container = QWidget()
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 10)
        # 返回上一级按钮（橙色）
        back_btn = QPushButton("返回上一级")
        back_btn.setFont(QFont("Arial", 11))
        back_btn.setFixedSize(120, 36)
        if self.on_back:
            back_btn.clicked.connect(self.on_back)
        else:
            back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        nav_layout.addWidget(back_btn)
        # 返回主页按钮（主色蓝色）
        home_btn = QPushButton("返回主页")
        home_btn.setFont(QFont("Arial", 11))
        home_btn.setFixedSize(100, 32)
        if self.on_home:
            home_btn.clicked.connect(self.on_home)
        else:
            home_btn.clicked.connect(self.close)
        home_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        nav_layout.addWidget(home_btn)
        nav_layout.addStretch()
        
        return nav_container

    def create_config_group(self):
        """创建配置分组"""
        group = QGroupBox("DM制作配置")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QGridLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 版本号
        layout.addWidget(QLabel("版本号："), 0, 0)
        self.version_edit = QLineEdit()
        self.version_edit.setFont(QFont("Arial", 11))
        self.version_edit.setPlaceholderText("请输入版本号，例如：v1.0.0")
        layout.addWidget(self.version_edit, 0, 1)
        
        # 车型名称
        layout.addWidget(QLabel("车型名称："), 1, 0)
        self.model_edit = QLineEdit()
        self.model_edit.setFont(QFont("Arial", 11))
        self.model_edit.setPlaceholderText("请输入车型名称")
        layout.addWidget(self.model_edit, 1, 1)
        
        return group

    def create_folder_group(self):
        """创建文件夹分组"""
        group = QGroupBox("文件夹设置")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QGridLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 输入文件夹
        layout.addWidget(QLabel("输入文件夹："), 0, 0)
        self.input_dir_edit = QLineEdit()
        self.input_dir_edit.setFont(QFont("Arial", 11))
        self.input_dir_edit.setReadOnly(True)
        self.input_dir_edit.setPlaceholderText("请选择输入文件夹...")
        layout.addWidget(self.input_dir_edit, 0, 1)
        
        input_browse_btn = QPushButton("浏览")
        input_browse_btn.setFont(QFont("Arial", 11))
        input_browse_btn.setFixedSize(80, 32)
        input_browse_btn.clicked.connect(self.browse_input_dir)
        input_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(input_browse_btn, 0, 2)
        
        # 输出文件夹
        layout.addWidget(QLabel("输出文件夹："), 1, 0)
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setFont(QFont("Arial", 11))
        self.output_dir_edit.setReadOnly(True)
        self.output_dir_edit.setPlaceholderText("请选择输出文件夹...")
        layout.addWidget(self.output_dir_edit, 1, 1)
        
        output_browse_btn = QPushButton("浏览")
        output_browse_btn.setFont(QFont("Arial", 11))
        output_browse_btn.setFixedSize(80, 32)
        output_browse_btn.clicked.connect(self.browse_output_dir)
        output_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(output_browse_btn, 1, 2)
        
        return group

    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # 确定按钮
        self.ok_btn = QPushButton("开始制作")
        self.ok_btn.setFont(QFont("Arial", 11, QFont.Bold))
        self.ok_btn.setFixedSize(120, 36)
        self.ok_btn.clicked.connect(self.run_dm)
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(self.ok_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setFont(QFont("Arial", 11))
        self.cancel_btn.setFixedSize(100, 36)
        self.cancel_btn.clicked.connect(self.cancel_dm)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(self.cancel_btn)
        
        return layout

    def setup_styles(self):
        """设置整体样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0;
                padding-top: 15px;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: #ecf0f1;
            }
            QLabel {
                color: #2c3e50;
                font-size: 11pt;
                font-weight: normal;
            }
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px;
                font-size: 11pt;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

    def browse_input_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择输入文件夹")
        if path:
            self.input_dir_edit.setText(path)

    def browse_output_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if path:
            self.output_dir_edit.setText(path)

    def run_dm(self):
        # 这里应调用实际DM制作程序
        self.status_label.setText("✓ DM制作已完成！")
        QMessageBox.information(self, "提示", "DM制作已完成！")

    def cancel_dm(self):
        # 这里应实现取消功能（暂未实现）
        self.status_label.setText("已取消（未实现实际停止功能）")
        QMessageBox.information(self, "提示", "已取消（未实现实际停止功能）")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    window = DMUtilsGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
