import os
import shutil
import zipfile
from shlex import shlex

import openpyxl
from uuid import uuid4

import lxml
import pandas as pd
from PIL import Image
from loguru import logger
from lxml import etree
import getpass
import time
import slxUtils
import xlsxwriter

user_map={"su.xin2":"苏鑫","bi.sheng1":"毕升","zeng.gujie":"曾顾杰",
          "fang.haiji":"方海积","fang.haiji":"冯建川","hou.ruiyu":"侯瑞宇",
          "hu.jie31":"胡杰","hu.qixun":"胡奇勋","jiang.xinxiu":"蒋新秀",
          "liu.junfu2":"刘隽夫","liu.yuzhu3":"刘玉柱","luo.ji2":"罗基",
          "mei.shuchi":"梅述池","shi.dengke":"史登科","wang.ke44":"王轲",
          "xu.rui21":"许睿","yang.nannan2":"杨楠楠","you.yang4":"游洋",
          "zhang.zezhong":"张泽中","yao.zhixiao":"姚智晓","zhao.wanshuan":"赵万栓",
          "yin.shaodong":"尹邵东",}


cellimages_rels_template_content = """
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
</Relationships>
"""

cellimages_template_content = """
<etc:cellImages xmlns:xdr="http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing"
                xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:etc="http://www.wps.cn/officeDocument/2017/etCustomData">

</etc:cellImages>
"""


def unzip_file(zip_path: str, extract_to: str):



def zip_file(file_or_dir_path: str, zip_path: str):



def get_image_dimensions(image_path: str):



def copy_image_to_excel_dir(image_path: str, excel_unzip_dir: str, ID: str):



def add_new_node_cell_images(image_path: str, excel_unzip_dir: str):



def add_new_node_cell_images_rels(image_file_name: str, RID: str, excel_unzip_dir: str):


def add_new_node_content_types(excel_unzip_dir: str, sheet_Id: int):


def add_new_node_workbook(excel_unzip_dir: str):


def add_new_node(image_path: str, unzip_dir_path: str, sheet_ID: int):



def add_sheet_data(unzip_file_path, sheet_name, ID, cell_index, row_index, sheet_ID: int):


def embed_image(excel_path: str, new_excel_path: str, sheet_name: str, head_name: list):


def modifyExcelCellValue(row_index,cell_index,sheet_root,value,value_sheet):


def writeDMExcelTemplate(file_path,car_name,vse_version,key_info_file_path,result_file_path):


if __name__ == '__main__':
    #zip_file("E:\项目\RTE应用\VSEAA2.0-SWE3-035_VSE2.0项目HT（春改）-D3车型差异性分析报告V1.0.0", "tt.xlsx")
    writeDMExcelTemplate("VSEAA2.0-SWE3-035_VSE2.0项目HT（春改）-D3车型差异性分析报告V1.0.0.xlsx","HT(春改)-D3","VSE2.00.40","关键信息",r"E:\c学习\DM_Auto_Test\x64\Debug\测试结果.xlsx")
