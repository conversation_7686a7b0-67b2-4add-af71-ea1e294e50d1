@echo off
chcp 65001 > nul
title VSETools 1.0 启动器

echo.
echo =============================================
echo            VSETools 1.0 启动器
echo =============================================
echo.

echo 正在启动 VSETools 1.0...
echo.

rem 检查Python是否安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)

rem 检查是否已安装依赖
python -c "import PyQt5" > nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖包...
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
    echo 依赖包安装完成
    echo.
)

rem 启动主程序
python main_gui.py

rem 如果程序异常退出，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
    pause
)
