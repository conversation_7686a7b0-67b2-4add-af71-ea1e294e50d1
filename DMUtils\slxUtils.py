import pandas as pd

import os
from lxml import etree
import shutil
from loguru import logger
import excelUtil

u8CCSoftVersNr = ["u8CCSoftVersNr.Char_Zero", "u8CCSoftVersNr.Char_One", "u8CCSoftVersNr.Char_Two",
                  "u8CCSoftVersNr.Char_Three", "u8CCSoftVersNr.Char_Four", "u8CCSoftVersNr.Char_Five",
                  "u8CCSoftVersNr.Char_Six", "u8CCSoftVersNr.Char_Seven", "u8CCSoftVersNr.Char_Eight",
                  "u8CCSoftVersNr.Char_Nine"]

carInfoDict = dict()


class CarInfo:
    def __init__(self, carCode, carConfig, carName):
        self.carCode = carCode
        self.carConfig = carConfig
        self.carName = carName

    def __str__(self):
        return self.carCode + "  " + self.carConfig + "  " + self.carName


def genCarIDInfo(carIDFile):


def changeGetCarIDFcn(slxFile, carInfo, version):



if __name__ == '__main__':
    info = genCarIDInfo(
        r"E:\VSEAA2.0_svn\01_Development\02_SWE_SoftwareEngineeringProcessGroup\03_SWE3_SoftwareDetailedDesignAndUnitConstruction\01_ASW\D3\08_车型应用\14_HT国内升版\01_车型匹配文件\VSEAA2.0-SWE1-014_VSE2.0项目HT车型-D3车型底盘电控关键信息V1.0.0.xlsx")
    # changeGetCarIDFcn("VSE_VehDataMngt.slx",info,"vse2.30.80")
