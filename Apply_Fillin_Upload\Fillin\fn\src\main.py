import logging
from datetime import datetime
from pathlib import Path
from config_loader import load_config_from_excel
from doc_processor import process_document

# 配置日志
log_dir = Path('../logs')
log_dir.mkdir(exist_ok=True)
log_file = log_dir / f'process_docx_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """
    主函数，处理输入目录中的所有 .docx 文件。
    """
    try:
        # 定义路径
        input_dir = Path('../inputs')
        output_dir = Path('../outputs')

        # 创建目录（如果不存在）
        input_dir.mkdir(exist_ok=True)
        output_dir.mkdir(exist_ok=True)

        # 加载配置
        config = load_config_from_excel()

        # 处理每个 .docx 文件
        for file_path in input_dir.glob('*.docx'):
            if '接口定义通知单' in file_path.name:
                process_document(str(file_path), output_dir, config)
            else:
                logger.info(f"跳过文件（不符合条件）：{file_path}")

        logger.info("处理完成")

    except Exception as e:
        logger.critical(f"程序因错误终止：{e}")
        raise

if __name__ == '__main__':
    main()