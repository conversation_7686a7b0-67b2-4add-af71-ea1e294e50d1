from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.cell.cell import MergedCell
from openpyxl.styles import Font
import re
import logging

logger = logging.getLogger(__name__)


def find_cell(ws: Worksheet, target_value: str, check_font: bool = False) -> tuple:
    """在工作表中查找包含目标值的单元格，可选择检查字体。

    Args:
        ws (Worksheet): openpyxl Worksheet对象。
        target_value (str): 要查找的值。
        check_font (bool): 是否检查字体（用于筛选日期单元格）。

    Returns:
        tuple: (row, col, font) 单元格位置和字体对象，未找到返回(None, None, None)。
    """
    for row in ws.iter_rows():
        for cell in row:
            if isinstance(cell, MergedCell):
                continue
            if cell.value and target_value.lower() in str(cell.value).lower():
                font = cell.font if check_font else None
                return cell.row, cell.column, font
    return None, None, None


def get_merged_cell_range(ws: Worksheet, row: int, col: int) -> tuple:
    """获取单元格是否为合并单元格及其范围。

    Args:
        ws (Worksheet): openpyxl Worksheet对象。
        row (int): 行号。
        col (int): 列号。

    Returns:
        tuple: (is_merged, start_row, start_col, end_row, end_col)。
    """
    for merged_range in ws.merged_cells.ranges:
        if merged_range.min_row <= row <= merged_range.max_row and merged_range.min_col <= col <= merged_range.max_col:
            return (True, merged_range.min_row, merged_range.min_col, merged_range.max_row, merged_range.max_col)
    return (False, row, col, row, col)


def fill_cell(ws: Worksheet, row: int, col: int, value: str):
    """在指定单元格右侧填充值。

    Args:
        ws (Worksheet): openpyxl Worksheet对象。
        row (int): 行号。
        col (int): 列号。
        value (str): 要填充的值。
    """
    is_merged, _, _, _, end_col = get_merged_cell_range(ws, row, col)
    target_col = end_col + 1
    target_cell = ws.cell(row=row, column=target_col)

    if isinstance(target_cell, MergedCell):
        logger.warning(f"目标单元格 ({row}, {target_col}) 是合并单元格，跳过填充")
        return

    target_cell.value = value
    logger.info(f"在 ({row}, {target_col}) 填充值: {value}")


def replace_project_vse(ws: Worksheet, project_code: str):
    """替换包含'项目VSE软件'的单元格内容。

    Args:
        ws (Worksheet): openpyxl Worksheet对象。
        project_code (str): 项目代号。
    """
    row, col, _ = find_cell(ws, "项目VSE软件")
    if row and col:
        cell = ws.cell(row=row, column=col)
        if isinstance(cell, MergedCell):
            logger.warning(f"单元格 ({row}, {col}) 是合并单元格，需定位到合并区域起始单元格")
            is_merged, start_row, start_col, _, _ = get_merged_cell_range(ws, row, col)
            if is_merged:
                cell = ws.cell(row=start_row, column=start_col)

        original_value = str(cell.value)
        new_value = re.sub(r'.*?(?=项目VSE软件)', '', original_value)
        new_value = project_code + new_value
        cell.value = new_value
        logger.info(f"在 ({cell.row}, {cell.column}) 将内容从 '{original_value}' 替换为 '{new_value}'")
    else:
        logger.warning(f"在Sheet {ws.title} 中未找到包含'项目VSE软件'的单元格")