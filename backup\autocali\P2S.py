import matlab.engine
import os
import sys


def simulink_run_transfer(target_path):
    # target_path = r'D:\应用开发组workingCalender\25Q2\自动标定Phase1\python\py2simulink\P2S\inputmat'

    # 获取目标路径下所有 .mat 文件的名称
    filename_Mat = [f for f in os.listdir(target_path) if f.endswith('.mat')]
    print(filename_Mat)
    # 遍历每个 .mat 文件并运行 Simulink 模型
    for mat_file in filename_Mat:
        mat_file_path = os.path.join(target_path, mat_file)
        print(mat_file_path)
        dataname = os.path.splitext(mat_file)[0]
        print(dataname)

        # 检查mat文件是否存在
        if not os.path.exists(mat_file_path):
            print(f"文件不存在: {mat_file_path}")
            continue

        eng = matlab.engine.start_matlab()
        eng.eval(f'load(\'{mat_file_path}\')', nargout=0)
        start_time = eng.eval(f'sig_AccelForward(1,1)')
        end_time = eng.eval(f'sig_ACU_LongitudeAcceleration_S(end,1)')
        print(type(end_time))
        print(end_time)
        eng.eval("open_system('transfer_mat.slx')", nargout=0)
        print("1")
        eng.eval(f"set_param('transfer_mat','StartTime','{start_time}')", nargout=0)
        eng.eval(f"set_param('transfer_mat','StopTime','{end_time}')", nargout=0)
        print("2")
        eng.eval('sim transfer_mat.slx', nargout=0)
        eng.eval(f"save('{dataname}.mat','ans')", nargout=0)
        print(f"save('{dataname}.mat','ans')")
        eng.eval('clear', nargout=0)
        eng.eval(f'load {dataname}.mat', nargout=0)
        eng.eval(
            'tout = ans.tout,sig_BCMPower_Gear_12D_S = ans.sig_BCMPower_Gear_12D_S, sig_Gear_Position = ans.sig_Gear_Position, sig_Gear_Status = ans.sig_Gear_Status, sig_EPB_Status = ans.sig_EPB_Status, sig_ACU_LongitudeAcceleration_S = ans.sig_ACU_LongitudeAcceleration_S, sig_ACU_LateralAcceleration_S = ans.sig_ACU_LateralAcceleration_S, sig_ACU_VerticalAcceleration_S = ans.sig_ACU_VerticalAcceleration_S, sig_ACU_Wx_RollRate_S = ans.sig_ACU_Wx_RollRate_S, sig_ACU_Wy_PitchRate_S = ans.sig_ACU_Wy_PitchRate_S, sig_ACU_YawRate_S = ans.sig_ACU_YawRate_S, sig_ACU_LongitudeAcceleration_St_S = ans.sig_ACU_LongitudeAcceleration_St_S, sig_ACU_LateralAcceleration_St_S = ans.sig_ACU_LateralAcceleration_St_S, sig_ACU_VerticalAcceleration_St_S = ans.sig_ACU_VerticalAcceleration_St_S, sig_ACU_Wx_RollRateSensor_St_S = ans.sig_ACU_Wx_RollRateSensor_St_S, sig_ACU_Wy_PitchRateSensor_St_S = ans.sig_ACU_Wy_PitchRateSensor_St_S, sig_ACU_YawRateSensor_St_S = ans.sig_ACU_YawRateSensor_St_S, sig_ABS_Active_122_S = ans.sig_ABS_Active_122_S, sig_TCS_Active_S = ans.sig_TCS_Active_S, sig_VDC_Active_222_S = ans.sig_VDC_Active_222_S, sig_ABS_Fault_122_S = ans.sig_ABS_Fault_122_S, sig_TCS_Fault_0F4_S = ans.sig_TCS_Fault_0F4_S, sig_VDC_Fault_123_S = ans.sig_VDC_Fault_123_S, sig_WheelSpeed_FL_122_S = ans.sig_WheelSpeed_FL_122_S, sig_WheelSpeed_FL_Status_122_S = ans.sig_WheelSpeed_FL_Status_122_S, sig_WheelSpeed_FR_122_S = ans.sig_WheelSpeed_FR_122_S, sig_WheelSpeed_FR_Status_122_S = ans.sig_WheelSpeed_FR_Status_122_S, sig_WheelSpeed_RL_122_S = ans.sig_WheelSpeed_RL_122_S, sig_WheelSpeed_RL_Status_122_S = ans.sig_WheelSpeed_RL_Status_122_S, sig_WheelSpeed_RR_122_S = ans.sig_WheelSpeed_RR_122_S, sig_WheelSpeed_RR_Status_122_S = ans.sig_WheelSpeed_RR_Status_122_S, sig_Actual_Throttle_Depth_S = ans.sig_Actual_Throttle_Depth_S, sig_Actual_Throttle_Dep_Effect_S = ans.sig_Actual_Throttle_Dep_Effect_S, sig_VCU_Brake_Depth_S = ans.sig_VCU_Brake_Depth_S, sig_VCU_Brake_Depth_Virtual_Value_S = ans.sig_VCU_Brake_Depth_Virtual_Value_S, sig_Veh_drv_sty = ans.sig_Veh_drv_sty, sig_IPB_BRAKE_PEDAL_STATUS = ans.sig_IPB_BRAKE_PEDAL_STATUS, sig_IPB_Plunger_Pressure_321_S = ans.sig_IPB_Plunger_Pressure_321_S, sig_IPB_PlungerPressure_Status_321_S = ans.sig_IPB_PlungerPressure_Status_321_S, sig_Front_Torque_241_S = ans.sig_Front_Torque_241_S, sig_Front_Torque_State_241_S = ans.sig_Front_Torque_State_241_S, sig_Rear_Torque_251_S = ans.sig_Rear_Torque_251_S, sig_Rear_Torque_State_251_S = ans.sig_Rear_Torque_State_251_S, sig_Torque_FL_0FC_S = ans.sig_Torque_FL_0FC_S, sig_Torque_FR_0FC_S = ans.sig_Torque_FR_0FC_S, sig_Torque_RL_0FC_S = ans.sig_Torque_RL_0FC_S, sig_Torque_RR_0FC_S = ans.sig_Torque_RR_0FC_S, sig_Torque_State_FL_0FC_S = ans.Torque_State_FL_0FC_S, sig_Torque_State_FR_0FC_S = ans.Torque_State_FR_0FC_S, sig_Torque_State_RL_0FC_S = ans.Torque_State_RL_0FC_S, sig_Torque_State_RR_0FC_S = ans.Torque_State_RR_0FC_S, sig_Sensor_Calibration_Stats_11F_S = ans.sig_Sensor_Calibration_Stats_11F_S, sig_Failure_Stats_OK_11F_S = ans.sig_Failure_Stats_OK_11F_S, sig_AccelForward = ans.sig_AccelForward, sig_AccelLateral = ans.sig_AccelLateral, sig_AngleSlip = ans.sig_AngleSlip,sig_Steering_Wheel_Angle_11F_S = ans.sig_Steering_Wheel_Angle_11F_S,sig_Steering_Wheel__Speed_11F_S = ans.sig_Steering_Wheel__Speed_11F_S',
            nargout=0)
        print("3")
        eng.eval('clear ans', nargout=0)
        # eng.eval(f"mkdir SimMat'", nargout=0)

        eng.eval(f" save('output_simmat/{dataname}.mat')", nargout=0)
        print(f"finishsave('{dataname}.mat')")

        # 停止 MATLAB 引擎
        eng.quit()


if __name__ == "__main__":
    # for i in range(len(sys.argv)):
    #     print(sys.argv[i])
    if len(sys.argv) > 1:
       param = sys.argv[1]
       simulink_run_transfer(param)
