"""
申请编号自动化程序
根据输入文件夹中的文件类型自动申请编号，并更新文件名和内容
"""

import os
import time
import logging
from pathlib import Path
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from file_number_filler import update_document
import config

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('apply_id_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ApplyIDAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.base_url = config.DMS_URL
        
        # 从配置文件获取信息
        self.username = config.USERNAME
        self.password = config.PASSWORD
        self.project_code = config.PROJECT_CODE
        
        # 文件类型映射
        self.file_type_mapping = {
            "DVP": "DVP-系统设计验证计划",
            "PPL": "PPL-车辆匹配计划", 
            "FN": "FN-接口定义/功能输入通知单"
        }
        
        # 输入和输出文件夹路径
        self.input_folder = Path("input_files")
        self.output_folder = Path("output_files")
        
        # 确保输出文件夹存在
        self.output_folder.mkdir(parents=True, exist_ok=True)
        
    def setup_driver(self):
        """设置Chrome浏览器 - 支持静默模式"""
        chrome_options = Options()
        
        # 检查是否启用静默模式
        try:
            headless_mode = getattr(config, 'HEADLESS_MODE', False)
        except AttributeError:
            headless_mode = False
        
        if headless_mode:
            logger.info("🔇 启用静默模式 - 浏览器将在后台运行")
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")  # 设置窗口大小以确保元素可见
        else:
            logger.info("🖥️ 显示模式 - 浏览器窗口可见")
            chrome_options.add_argument("--start-maximized")
        
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 禁用保存密码提示
        prefs = {
            "credentials_enable_service": False,
            "profile.password_manager_enabled": False
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # 设置ChromeDriver路径
        try:
            # 首先尝试在当前目录查找chromedriver
            current_dir = Path(__file__).parent
            chromedriver_paths = [
                current_dir / "chromedriver.exe",  # Apply文件夹内
                current_dir.parent / "chromedriver.exe",  # Apply文件夹外一级
                "chromedriver.exe",  # 系统PATH中
                "chromedriver"  # Linux/Mac系统
            ]
            
            chromedriver_path = None
            for path in chromedriver_paths:
                if isinstance(path, Path) and path.exists():
                    chromedriver_path = str(path)
                    logger.info(f"找到ChromeDriver: {chromedriver_path}")
                    break
                elif isinstance(path, str):
                    # 尝试在PATH中查找
                    import shutil
                    if shutil.which(path):
                        chromedriver_path = path
                        logger.info(f"在系统PATH中找到ChromeDriver: {chromedriver_path}")
                        break
            
            if chromedriver_path:
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info(f"使用指定路径的ChromeDriver: {chromedriver_path}")
            else:
                # 如果找不到指定路径的chromedriver，尝试使用默认方式
                logger.warning("未找到指定路径的ChromeDriver，尝试使用默认方式")
                self.driver = webdriver.Chrome(options=chrome_options)
                logger.info("使用默认ChromeDriver")
                
        except Exception as e:
            logger.error(f"ChromeDriver设置失败: {str(e)}")
            logger.info("请确保:")
            logger.info("1. Chrome浏览器已安装")
            logger.info("2. ChromeDriver已下载并放在正确位置")
            logger.info("3. ChromeDriver版本与Chrome浏览器版本匹配")
            raise
        
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, config.WAIT_TIMEOUT)
        logger.info("浏览器已启动并最大化")
        
    def login(self):
        """登录系统"""
        try:
            logger.info("开始登录...")
            self.driver.get(self.base_url)
            time.sleep(config.OPERATION_DELAY + 1)
            
            # 点击登录按钮
            login_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='app']/section/header/div/div[2]/button/span")
            ))
            login_btn.click()
            logger.info("已点击登录按钮")
            time.sleep(config.OPERATION_DELAY)
            
            # 输入用户名
            username_field = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div/div/div/input")
            ))
            username_field.clear()
            username_field.send_keys(self.username)
            logger.info("已输入用户名")
            time.sleep(1)
            
            # 输入密码
            password_field = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[2]/div/div/input")
            ))
            password_field.clear()
            password_field.send_keys(self.password)
            logger.info("已输入密码")
            time.sleep(1)
            
            # 点击登录
            submit_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//*[@id='container']/div/div[2]/div[2]/form/div[4]/div/button")
            ))
            submit_btn.click()
            logger.info("已点击登录提交按钮")
            time.sleep(5)  # 等待登录完成
            
            logger.info("登录成功")
            
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            raise
            
    def close_popups(self):
        """关闭登录后的弹窗 - 优化版本：循环快速检测，大幅缩短等待时间"""
        try:
            logger.info("🔄 开始弹窗快速检测与关闭...")
            
            # 初始等待时间缩短
            time.sleep(1)
            
            # 采用循环快速检测策略，大幅缩短等待时间
            max_attempts = 8  # 最多尝试8次
            check_interval = 0.6  # 每次检测间隔0.6秒（从2秒优化到0.6秒）
            popup_found = False
            
            for attempt in range(max_attempts):
                logger.info(f"  📍 第 {attempt + 1}/{max_attempts} 次弹窗检测...")
                current_popup_closed = False
                
                # 尝试关闭智能小助手弹窗
                try:
                    close_btn1 = self.driver.find_element(By.CSS_SELECTOR, ".maxkb-close")
                    if close_btn1.is_displayed():
                        close_btn1.click()
                        logger.info("    ✅ 关闭智能小助手弹窗")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试点击"我知道了"按钮
                try:
                    know_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), '我知道了')]")
                    if know_btn.is_displayed():
                        know_btn.click()
                        logger.info("    ✅ 点击'我知道了'按钮")
                        current_popup_closed = True
                        popup_found = True
                        time.sleep(0.5)
                except (NoSuchElementException, Exception):
                    pass
                    
                # 尝试关闭文档管控公告弹窗
                try:
                    notification_selectors = [
                        ".el-notification .el-icon",
                        ".el-notification__closeBtn",
                        "#notification_1 .el-icon",
                        ".layout-notification .el-icon"
                    ]
                    
                    for selector in notification_selectors:
                        try:
                            close_btn2 = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if close_btn2.is_displayed():
                                close_btn2.click()
                                logger.info(f"    ✅ 关闭文档管控公告弹窗 (选择器: {selector})")
                                current_popup_closed = True
                                popup_found = True
                                time.sleep(0.5)
                                break
                        except (NoSuchElementException, Exception):
                            continue
                except Exception:
                    pass
                
                # 如果本次检测没有关闭任何弹窗
                if not current_popup_closed:
                    # 如果之前关闭过弹窗，再给一次机会检测新弹窗
                    if popup_found and attempt < max_attempts - 1:
                        time.sleep(check_interval)
                        continue
                    # 如果从未发现弹窗，可以提前结束
                    elif not popup_found and attempt >= 3:
                        logger.info("    📝 连续多次未发现弹窗，提前结束检测")
                        break
                    else:
                        time.sleep(check_interval)
                else:
                    # 关闭了弹窗后，短暂等待看是否有新弹窗
                    time.sleep(0.8)
                    
            if popup_found:
                logger.info("🎉 弹窗关闭流程完成 (发现并处理了弹窗)")
                time.sleep(2.5)  # 弹窗关闭后需要更多时间等待页面稳定
            else:
                logger.info("✨ 弹窗检测完成 (未发现弹窗)")
                time.sleep(1.5)  # 未发现弹窗时适度等待
                
        except Exception as e:
            logger.warning(f"⚠️ 关闭弹窗过程中出现问题，继续执行: {str(e)}")
            # 不抛出异常，继续执行后续流程
            
    # def navigate_to_apply_number(self):
    #     """导航到申请编号页面 - 优化版本：缩短等待时间，提升执行效率"""
    #     try:
    #         logger.info("🧭 导航到申请编号页面...")
    #         time.sleep(1.5)  # 恢复等待时间，让页面有足够时间响应
    #
    #         # 使用多种方式尝试点击申请编号按钮
    #         apply_button_selectors = [
    #             "//*[@id='main']/div/div/div/div/div/div[3]/span",
    #             "//span[contains(text(), '申请编号')]",
    #             ".NumberApplication span",
    #             "//div[contains(@class, 'NumberApplication')]//span"
    #         ]
    #
    #         button_clicked = False
    #         for i, selector in enumerate(apply_button_selectors):
    #             try:
    #                 if selector.startswith("//") or selector.startswith("/"):
    #                     apply_btn = WebDriverWait(self.driver, 8).until(EC.element_to_be_clickable((By.XPATH, selector)))
    #                 else:
    #                     apply_btn = WebDriverWait(self.driver, 8).until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
    #                 apply_btn.click()
    #                 logger.info(f"  ✅ 已点击申请编号按钮 (使用选择器 {i+1}: {selector[:30]}...)")
    #                 button_clicked = True
    #                 break
    #             except TimeoutException:
    #                 logger.info(f"  ⚠️ 选择器 {i+1} 未找到，尝试下一个...")
    #                 continue
    #
    #         if not button_clicked:
    #             raise Exception("❌ 无法找到申请编号按钮")
    #
    #         time.sleep(2.5)  # 恢复等待时间，确保模态框完全加载
    #
    #         # 验证模态框是否打开
    #         try:
    #             modal = WebDriverWait(self.driver, 8).until(EC.presence_of_element_located(
    #                 (By.CSS_SELECTOR, ".el-drawer")
    #             ))
    #             logger.info("  🎯 申请编号模态框已成功打开")
    #         except TimeoutException:
    #             raise Exception("❌ 申请编号模态框未能打开")
    #
    #     except Exception as e:
    #         logger.error(f"❌ 导航到申请编号页面失败: {str(e)}")
    #         raise

    def navigate_to_apply_number(self):
        """直接访问申请编号页面 - 终极优化版本"""

        logger.info("🧭 直接导航到申请编号页面...")
        target_url = "https://gcy.byd.com/dms/#/document/myNumber?type=numberApplication"

        # 智能页面跳转（带当前页面状态检测）
        if self.driver.current_url != target_url:
            self.driver.get(target_url)
            logger.info(f"  ✅ 已直接访问申请编号页面: {target_url[:45]}...")

        time.sleep(5)

    def identify_file_type(self, filename):
        """根据文件名识别文件类型"""
        filename_lower = filename.lower()
        
        if "dvp" in filename_lower or "设计验证计划" in filename:
            return "DVP"
        elif "ppl" in filename_lower or "匹配" in filename:
            return "PPL"
        elif any(keyword in filename for keyword in ["接口定义", "通知单", "FN", "fn"]):
            return "FN"
        else:
            # 默认为FN类型
            return "FN"
            
    def get_files_by_type(self):
        """获取输入文件夹中按类型分组的文件"""
        if not self.input_folder.exists():
            self.input_folder.mkdir(parents=True, exist_ok=True)
            logger.warning(f"输入文件夹不存在，已创建: {self.input_folder}")
            return {}
            
        files_by_type = {"DVP": [], "PPL": [], "FN": []}
        
        for file_path in self.input_folder.iterdir():
            if file_path.is_file() and file_path.suffix in ['.docx', '.xlsx']:
                file_type = self.identify_file_type(file_path.name)
                files_by_type[file_type].append(file_path)
                logger.info(f"文件 {file_path.name} 识别为 {file_type} 类型")
                
        return files_by_type
        
    def fill_basic_info(self, doc_type):
        """填写基本信息 - 优化版本：直接用文本选择器，大幅提升速度和兼容性
        流程：文件类型选择（项目文件）→ 项目文档（DVP等）→ 项目代号
        核心优化：
        1. 文件类型选择直接使用文本选择器"//span[text()='项目文件']"，速度最快
        2. 项目文档和项目代号采用"点击输入框→输入关键字→文本选择器选浮动框项"模式
        3. 兼容JSON ID变动，提升页面结构变化的适应性
        """
        try:
            logger.info(f"📝 开始填写基本信息 (文件类型: {doc_type}) - 使用优化策略...")
            time.sleep(1)  # 短暂等待模态框稳定
            
            # === 第一阶段：文件类型选择（选择"项目文件"）===
            logger.info("🔸 阶段1：文件类型选择 - 项目文件 (使用优化的文本选择器)")
            logger.info("  - 点击文件类型下拉框")
            
            # 【核心优化】简化选择器，提升执行速度
            try:
                file_type_dropdown = self.wait.until(EC.element_to_be_clickable(
                    (By.CSS_SELECTOR, ".el-form-item .el-select")
                ))
                logger.info("  - 找到文件类型下拉框")
            except TimeoutException:
                # 备用选择器
                file_type_dropdown = self.wait.until(EC.element_to_be_clickable(
                    (By.CSS_SELECTOR, "#el-collapse-content-14 > div > form > div > div.el-form-item__content > div > div > div.el-select__suffix > i > svg")
                ))
                logger.info("  - 使用备用选择器找到文件类型下拉框")
                
            file_type_dropdown.click()
            time.sleep(1)  # 缩短等待时间（从2秒优化到1秒）
            
            # 【核心优化】直接使用文本选择器选择"项目文件"，速度最快最可靠
            logger.info("  - 📦 使用文本选择器直接选择'项目文件' (速度最快)")
            try:
                project_file_option = WebDriverWait(self.driver, 6).until(EC.element_to_be_clickable(
                    (By.XPATH, "//span[text()='项目文件']")
                ))
                project_file_option.click()
                logger.info("  - ✅ 成功选择项目文件 (文本选择器)")
            except TimeoutException:
                # 备用方案：尝试其他选择器
                logger.info("  - ⚠️ 文本选择器失败，尝试备用方案")
                selectors_to_try = [
                    (By.ID, "el-id-7049-24"),
                    (By.XPATH, "//li[@id='el-id-7049-24']"),
                    (By.XPATH, "//li[contains(text(), '项目文件')]"),
                    (By.CSS_SELECTOR, ".el-select-dropdown .el-select-dropdown__item:first-child"),
                ]
                
                project_file_selected = False
                for i, (by_type, selector) in enumerate(selectors_to_try):
                    try:
                        project_file_option = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                            (by_type, selector)
                        ))
                        project_file_option.click()
                        project_file_selected = True
                        logger.info(f"  - ✅ 成功使用备用选择器 {i+1}")
                        break
                    except TimeoutException:
                        continue
                
                if not project_file_selected:
                    # 最后尝试键盘操作
                    logger.info("  - 🎹 所有选择器失败，尝试键盘操作")
                    file_type_dropdown.send_keys(Keys.ARROW_DOWN)
                    time.sleep(0.5)
                    file_type_dropdown.send_keys(Keys.ENTER)
            
            time.sleep(0.8)  # 缩短等待时间（从1秒优化到0.8秒）
            logger.info("  ✅ 文件类型选择完成：项目文件")
            
            # === 第二阶段：项目文档选择（DVP/PPL/FN等）- 优化输入方式 ===
            logger.info(f"🔸 阶段2：项目文档选择 - {self.file_type_mapping[doc_type]} (优化输入方式)")
            
            # 【核心优化】点击项目文档输入框，输入关键字，然后用文本选择器选择
            logger.info("  - 📝 点击项目文档输入框并输入关键字")
            try:
                # 使用更精确的xpath，定位到项目文档的输入框（第二个.el-form-item）
                project_doc_input = WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable(
                    (By.XPATH, "//label[text()='项目文档']/../../div[@class='el-form-item__content']//input[@type='text']")
                ))
                logger.info("  - 找到项目文档输入框(使用label定位)")
            except TimeoutException:
                # 如果上面的方法失败，尝试其他选择器
                logger.info("  - 主选择器未找到，尝试其他选择器")
                selectors = [
                    "//div[contains(@class,'el-form-item')][2]//input[@role='combobox']",  # 第二个表单项的输入框
                    "//span[text()='DVP-系统设计验证计划']/../..//input[@type='text']",  # 通过已显示的文本定位
                    ".el-form-item:nth-child(2) .el-select input[type='text']",  # CSS选择器
                ]
                
                project_doc_input = None
                for selector in selectors:
                    try:
                        if selector.startswith("//"):
                            project_doc_input = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                                (By.XPATH, selector)
                            ))
                        else:
                            project_doc_input = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                                (By.CSS_SELECTOR, selector)
                            ))
                        logger.info(f"  - 找到项目文档输入框(使用选择器: {selector[:40]}...)")
                        break
                    except TimeoutException:
                        continue
                
                if not project_doc_input:
                    raise Exception("❌ 无法找到项目文档输入框")
            
            # 点击输入框并输入关键字
            project_doc_input.click()
            time.sleep(0.5)
            project_doc_input.clear()
            project_doc_input.send_keys(doc_type)
            logger.info(f"  - 已输入关键字: {doc_type}")
            time.sleep(1)  # 等待下拉选项出现
            
            # 【核心优化】使用文本选择器选择具体的项目文档类型（最可靠的方式）
            doc_option_text = self.file_type_mapping[doc_type]
            logger.info(f"  - 📦 使用文本选择器选择: {doc_option_text}")
            
            try:
                # 优先使用文本选择，减少选择器数量
                selectors_to_try = [
                    f"//div[contains(@class, 'el-select-dropdown')]//span[text()='{doc_option_text}']",  # 在下拉框中的span
                    f"//ul[@role='listbox']//span[text()='{doc_option_text}']",  # 在listbox中的span
                ]
                
                doc_option = None
                for i, selector in enumerate(selectors_to_try):
                    try:
                        logger.info(f"  - 尝试选择器 {i+1}: {selector[:50]}...")
                        doc_option = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                            (By.XPATH, selector)
                        ))
                        doc_option.click()
                        logger.info(f"  - ✅ 成功使用选择器 {i+1}: {doc_option_text}")
                        break
                    except TimeoutException:
                        logger.info(f"  - 选择器 {i+1} 未找到，尝试下一个")
                        continue
                
                if doc_option is None:
                    raise TimeoutException("所有文本选择器都失败")
                    
            except TimeoutException:
                # 备用方案：如果是DVP，尝试JSON记录的选择器
                if doc_type == "DVP":
                    try:
                        doc_option = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                            (By.XPATH, "//*[@id='el-id-7049-95']/span")
                        ))
                        doc_option.click()
                        logger.info("  - ✅ 成功使用DVP专用选择器")
                    except TimeoutException:
                        raise Exception(f"❌ 无法找到项目文档选项: {doc_option_text}")
                else:
                    raise Exception(f"❌ 无法找到项目文档选项: {doc_option_text}")
            
            time.sleep(0.8)
            logger.info(f"  ✅ 项目文档选择完成：{doc_option_text}")
            
            # === 第三阶段：项目代号选择 - 优化输入方式 ===
            logger.info(f"🔸 阶段3：项目代号选择 - {self.project_code} (优化输入方式)")
            
            # 【核心优化】点击项目代号输入框，输入代号，然后用文本选择器选择
            logger.info("  - 📝 点击项目代号输入框并输入代号")
            try:
                # 使用更精确的xpath，定位到项目代号的输入框（第三个.el-form-item）
                project_code_input = WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable(
                    (By.XPATH, "//label[text()='项目代号']/../../div[@class='el-form-item__content']//input[@type='text']")
                ))
                logger.info("  - 找到项目代号输入框(使用label定位)")
            except TimeoutException:
                # 如果上面的方法失败，尝试其他选择器
                logger.info("  - 主选择器未找到，尝试其他选择器")
                selectors = [
                    "//div[contains(@class,'el-form-item')][3]//input[@role='combobox']",  # 第三个表单项的输入框
                    "//span[text()='HYEB']/../..//input[@type='text']",  # 通过已显示的文本定位
                    ".el-form-item:nth-child(3) .el-select input[type='text']",  # CSS选择器
                ]
                
                project_code_input = None
                for selector in selectors:
                    try:
                        if selector.startswith("//"):
                            project_code_input = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                                (By.XPATH, selector)
                            ))
                        else:
                            project_code_input = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                                (By.CSS_SELECTOR, selector)
                            ))
                        logger.info(f"  - 找到项目代号输入框(使用选择器: {selector[:40]}...)")
                        break
                    except TimeoutException:
                        continue
                
                if not project_code_input:
                    raise Exception("❌ 无法找到项目代号输入框")
            
            # 点击输入框并输入项目代号
            project_code_input.click()
            time.sleep(0.5)
            project_code_input.clear()
            project_code_input.send_keys(self.project_code)
            logger.info(f"  - 已输入项目代号: {self.project_code}")
            time.sleep(1)  # 等待下拉选项出现
            
            # 【核心优化】使用文本选择器选择项目代号选项（兼容ID变动）
            logger.info(f"  - 📦 使用文本选择器选择项目代号: {self.project_code}")
            try:
                # 按用户要求，去掉前两种选择器，只保留下拉框和listbox中的span
                selectors_to_try = [
                    f"//div[contains(@class, 'el-select-dropdown')]//span[text()='{self.project_code}']",  # 在下拉框中的span
                    f"//ul[@role='listbox']//span[text()='{self.project_code}']",  # 在listbox中的span
                ]
                
                code_option = None
                for i, selector in enumerate(selectors_to_try):
                    try:
                        logger.info(f"  - 尝试选择器 {i+1}: {selector}")
                        code_option = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                            (By.XPATH, selector)
                        ))
                        code_option.click()
                        logger.info(f"  - ✅ 成功使用选择器 {i+1}: {self.project_code}")
                        break
                    except TimeoutException:
                        logger.info(f"  - 选择器 {i+1} 未找到，尝试下一个")
                        continue
                
                if code_option is None:
                    raise TimeoutException("所有文本选择器都失败")
                    
            except TimeoutException:
                # 备用方案：尝试JSON记录的选择器
                try:
                    code_option = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                        (By.XPATH, "//*[@id='el-id-7049-691']/span")
                    ))
                    code_option.click()
                    logger.info("  - ✅ 成功使用JSON记录的选择器")
                except TimeoutException:
                    raise Exception(f"❌ 无法找到项目代号选项: {self.project_code}")
            
            time.sleep(0.8)
            logger.info(f"  ✅ 项目代号选择完成：{self.project_code}")
            logger.info("🎉 基本信息填写全部完成！")
            logger.info(f"   ├─ 文件类型: 项目文件")
            logger.info(f"   ├─ 项目文档: {self.file_type_mapping[doc_type]}")
            logger.info(f"   └─ 项目代号: {self.project_code}")
            
        except Exception as e:
            logger.error(f"❌ 填写基本信息失败: {str(e)}")
            # 截图帮助调试
            try:
                self.driver.save_screenshot("fill_basic_info_error.png")
                logger.info("📸 已保存错误截图: fill_basic_info_error.png")
            except:
                pass
            raise
            
    def add_files_for_numbering(self, files):
        """为文件添加编号申请行，按照JSON描述的操作序列"""
        try:
            generated_numbers = {}
            
            for i, file_path in enumerate(files):
                logger.info(f"为文件添加编号申请: {file_path.name}")
                
                # 步骤1: 点击新增行按钮 - 使用多种选择器提高可靠性
                logger.info("🔘 点击新增行按钮")
                
                # 多种新增行按钮选择器
                add_row_selectors = [
                    # JSON中记录的选择器
                    "//*[@id='el-collapse-content-19']/div/button/span",
                    # 基于HTML结构的选择器
                    "//button[.//span[text()='新增行 ']]",
                    "//button[.//span[contains(text(), '新增行')]]",
                    # 通过按钮类和文本结合
                    "//button[@class='el-button el-button--primary el-tooltip__trigger'][.//span[contains(text(), '新增行')]]",
                    # CSS选择器
                    ".el-button--primary span:contains('新增行')",
                    # 更宽泛的选择器
                    "//button[@type='button'][contains(@class, 'el-button--primary')]//span[contains(text(), '新增')]",
                    # 基于collapse content的选择器
                    "//div[contains(@id, 'el-collapse-content')]//button[contains(@class, 'el-button--primary')]",
                ]
                
                add_row_clicked = False
                for i, selector in enumerate(add_row_selectors):
                    try:
                        logger.info(f"  📝 尝试新增行选择器 {i+1}: {selector[:50]}...")
                        
                        if selector.startswith("//") or selector.startswith("/"):
                            add_row_btn = WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable(
                                (By.XPATH, selector)
                            ))
                        else:
                            add_row_btn = WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable(
                                (By.CSS_SELECTOR, selector)
                            ))
                        
                        # 确保按钮可见并可点击
                        if add_row_btn.is_displayed() and add_row_btn.is_enabled():
                            # 滚动到按钮位置
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", add_row_btn)
                            time.sleep(0.5)
                            
                            # 点击按钮
                            add_row_btn.click()
                            logger.info(f"  ✅ 成功使用选择器 {i+1} 点击新增行按钮")
                            add_row_clicked = True
                            break
                        else:
                            logger.info(f"  ⚠️ 选择器 {i+1} 找到按钮但按钮不可用")
                            
                    except TimeoutException:
                        logger.info(f"  ⚠️ 选择器 {i+1} 超时，尝试下一个...")
                        continue
                    except Exception as e:
                        logger.info(f"  ⚠️ 选择器 {i+1} 出错: {str(e)}")
                        continue
                
                if not add_row_clicked:
                    # 最后尝试：寻找所有可能的新增按钮
                    logger.info("  🔍 所有选择器失败，尝试寻找所有包含'新增'的按钮...")
                    try:
                        all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                        for btn in all_buttons:
                            try:
                                btn_text = btn.text.strip()
                                if "新增" in btn_text and btn.is_displayed() and btn.is_enabled():
                                    self.driver.execute_script("arguments[0].scrollIntoView(true);", btn)
                                    time.sleep(0.5)
                                    btn.click()
                                    logger.info(f"  ✅ 通过遍历按钮找到并点击新增行按钮: '{btn_text}'")
                                    add_row_clicked = True
                                    break
                            except:
                                continue
                    except Exception as e:
                        logger.error(f"  ❌ 遍历按钮方式也失败: {str(e)}")
                
                if not add_row_clicked:
                    raise Exception("❌ 所有新增行按钮选择器都失败了")
                
                time.sleep(2)  # 等待新行添加完成
                
                # 步骤2: 填写文档名称 - 直接使用原文件名，优化选择器
                doc_name = file_path.stem  # 使用原文件名，不加项目代号前缀
                logger.info(f"📝 填写文档名称: {doc_name}")
                
                # 多种文档名称输入框选择器，优化等待时间
                doc_name_selectors = [
                    # 基于表格结构的选择器（最可靠）
                    "tbody tr:last-child td:nth-child(2) input.el-input__inner",
                    "tbody tr:last-child td:nth-child(2) .el-input__inner", 
                    # JSON中记录的ID（可能会变）
                    "el-id-7049-707",
                    # 通过表格结构定位最新行
                    "//table//tbody//tr[last()]//td[2]//input[@type='text']",
                    "//table//tbody//tr[last()]//td[2]//input[@class='el-input__inner']",
                    # 更宽泛的选择器
                    ".el-table tbody tr:last-child .el-input__inner",
                    "//div[contains(@class,'el-table')]//tbody//tr[position()=last()]//input"
                ]
                
                doc_name_input = None
                for i, selector in enumerate(doc_name_selectors):
                    try:
                        logger.info(f"  📝 尝试文档名称输入框选择器 {i+1}: {selector[:40]}...")
                        
                        if selector.startswith("//") or selector.startswith("/"):
                            doc_name_input = WebDriverWait(self.driver, 6).until(EC.element_to_be_clickable(
                                (By.XPATH, selector)
                            ))
                        elif selector.startswith("el-id"):
                            doc_name_input = WebDriverWait(self.driver, 6).until(EC.element_to_be_clickable(
                                (By.ID, selector)
                            ))
                        else:
                            doc_name_input = WebDriverWait(self.driver, 6).until(EC.element_to_be_clickable(
                                (By.CSS_SELECTOR, selector)
                            ))
                        
                        if doc_name_input.is_displayed() and doc_name_input.is_enabled():
                            logger.info(f"  ✅ 成功找到文档名称输入框，使用选择器 {i+1}")
                            break
                        else:
                            logger.info(f"  ⚠️ 选择器 {i+1} 找到输入框但不可用")
                            doc_name_input = None
                            
                    except TimeoutException:
                        logger.info(f"  ⚠️ 选择器 {i+1} 超时，尝试下一个...")
                        continue
                    except Exception as e:
                        logger.info(f"  ⚠️ 选择器 {i+1} 出错: {str(e)}")
                        continue
                
                if not doc_name_input:
                    # 最后尝试：找所有输入框
                    logger.info("  🔍 所有选择器失败，寻找最后一个可用输入框...")
                    try:
                        all_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input.el-input__inner")
                        for inp in reversed(all_inputs):  # 从后往前找
                            if inp.is_displayed() and inp.is_enabled():
                                doc_name_input = inp
                                logger.info("  ✅ 通过遍历找到可用输入框")
                                break
                    except Exception as e:
                        logger.error(f"  ❌ 遍历输入框也失败: {str(e)}")
                
                if not doc_name_input:
                    raise Exception("❌ 无法找到文档名称输入框")
                
                # 清空并输入文档名称
                doc_name_input.clear()
                doc_name_input.send_keys(doc_name)
                time.sleep(1)  # 短暂等待输入完成
                
                # 步骤3: 点击生成编号按钮 - 优化选择器和等待时间
                logger.info("🔢 点击生成编号按钮")
                
                # 多种生成编号按钮选择器
                generate_btn_selectors = [
                    # 基于表格结构的选择器（最可靠）
                    "tbody tr:last-child td:nth-child(3) button",
                    "tbody tr:last-child td:nth-child(3) .el-button",
                    # JSON中记录的完整路径
                    "//*[@id='el-collapse-content-19']/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[3]/div/button/span",
                    # 基于表格结构的xpath
                    "//table//tbody//tr[last()]//td[3]//button",
                    "//table//tbody//tr[last()]//td[3]//button[contains(@class,'el-button')]",
                    # 通过按钮文本
                    "//button[.//span[contains(text(), '生成编号')]]",
                    # 更宽泛的选择器
                    ".el-table tbody tr:last-child td:nth-child(3) button"
                ]
                
                generate_btn = None
                for i, selector in enumerate(generate_btn_selectors):
                    try:
                        logger.info(f"  📝 尝试生成编号按钮选择器 {i+1}: {selector[:40]}...")
                        
                        if selector.startswith("//") or selector.startswith("/"):
                            generate_btn = WebDriverWait(self.driver, 6).until(EC.element_to_be_clickable(
                                (By.XPATH, selector)
                            ))
                        else:
                            generate_btn = WebDriverWait(self.driver, 6).until(EC.element_to_be_clickable(
                                (By.CSS_SELECTOR, selector)
                            ))
                        
                        if generate_btn.is_displayed() and generate_btn.is_enabled():
                            logger.info(f"  ✅ 成功找到生成编号按钮，使用选择器 {i+1}")
                            break
                        else:
                            logger.info(f"  ⚠️ 选择器 {i+1} 找到按钮但不可用")
                            generate_btn = None
                            
                    except TimeoutException:
                        logger.info(f"  ⚠️ 选择器 {i+1} 超时，尝试下一个...")
                        continue
                    except Exception as e:
                        logger.info(f"  ⚠️ 选择器 {i+1} 出错: {str(e)}")
                        continue
                
                if not generate_btn:
                    # 最后尝试：找所有包含"生成"的按钮
                    logger.info("  🔍 所有选择器失败，寻找包含'生成'的按钮...")
                    try:
                        all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                        for btn in all_buttons:
                            if "生成" in btn.text and btn.is_displayed() and btn.is_enabled():
                                generate_btn = btn
                                logger.info(f"  ✅ 通过遍历找到生成按钮: '{btn.text.strip()}'")
                                break
                    except Exception as e:
                        logger.error(f"  ❌ 遍历按钮也失败: {str(e)}")
                
                if not generate_btn:
                    raise Exception("❌ 无法找到生成编号按钮")
                
                # 滚动到按钮并点击
                self.driver.execute_script("arguments[0].scrollIntoView(true);", generate_btn)
                time.sleep(0.5)
                generate_btn.click()
                time.sleep(3)  # 等待编号生成，保持3秒等待
                
                # 步骤4: 获取生成的编号 - 优化选择器和等待逻辑
                logger.info("📋 获取生成的编号")
                
                # 多种编号显示选择器
                number_selectors = [
                    # 基于表格结构的选择器（最可靠）
                    "tbody tr:last-child td:nth-child(4) span",
                    "tbody tr:last-child td:nth-child(4) div span",
                    # JSON中记录的完整路径
                    "//*[@id='el-collapse-content-19']/div/form/div/div/div[3]/div/div/div/table/tbody/tr/td[4]/div/span",
                    # 基于表格结构的xpath
                    "//table//tbody//tr[last()]//td[4]//span",
                    "//table//tbody//tr[last()]//td[4]//div//span",
                    # 更宽泛的选择器
                    ".el-table tbody tr:last-child td:nth-child(4) span"
                ]
                
                generated_number = ""
                number_element = None
                
                # 先等待编号生成完成（检查按钮状态变化）
                try:
                    WebDriverWait(self.driver, 10).until(
                        lambda driver: not generate_btn.get_attribute("aria-disabled") or 
                                     generate_btn.get_attribute("disabled") is None
                    )
                    logger.info("  📝 编号生成按钮状态已恢复")
                except TimeoutException:
                    logger.info("  ⚠️ 编号生成按钮状态检查超时，继续获取编号")
                
                # 尝试获取生成的编号
                for i, selector in enumerate(number_selectors):
                    try:
                        logger.info(f"  📝 尝试编号选择器 {i+1}: {selector[:40]}...")
                        
                        if selector.startswith("//") or selector.startswith("/"):
                            number_element = WebDriverWait(self.driver, 8).until(EC.presence_of_element_located(
                                (By.XPATH, selector)
                            ))
                        else:
                            number_element = WebDriverWait(self.driver, 8).until(EC.presence_of_element_located(
                                (By.CSS_SELECTOR, selector)
                            ))
                        
                        generated_number = number_element.text.strip()
                        if generated_number and generated_number != "" and len(generated_number) > 5:
                            logger.info(f"  ✅ 成功获取编号，使用选择器 {i+1}: {generated_number}")
                            break
                        else:
                            logger.info(f"  ⚠️ 选择器 {i+1} 获取到空编号或无效编号: '{generated_number}'")
                            generated_number = ""
                            
                    except TimeoutException:
                        logger.info(f"  ⚠️ 选择器 {i+1} 超时，尝试下一个...")
                        continue
                    except Exception as e:
                        logger.info(f"  ⚠️ 选择器 {i+1} 出错: {str(e)}")
                        continue
                
                if not generated_number:
                    # 最后尝试：找所有span元素，寻找编号格式
                    logger.info("  🔍 所有选择器失败，寻找符合编号格式的文本...")
                    try:
                        all_spans = self.driver.find_elements(By.TAG_NAME, "span")
                        for span in all_spans:
                            text = span.text.strip()
                            # 检查是否符合编号格式：项目代号_类型_A19-数字
                            if text and "_" in text and "A19-" in text and len(text) > 10:
                                generated_number = text
                                number_element = span
                                logger.info(f"  ✅ 通过遍历找到编号: {generated_number}")
                                break
                    except Exception as e:
                        logger.error(f"  ❌ 遍历span也失败: {str(e)}")
                
                if generated_number and generated_number != "":
                    generated_numbers[file_path.name] = generated_number
                    logger.info(f"✅ 成功生成编号: {generated_number}")
                    
                    # 点击选中编号（JSON中的操作）
                    if number_element:
                        try:
                            for _ in range(3):
                                number_element.click()
                                time.sleep(0.2)
                            logger.info("📝 已选中生成的编号")
                        except:
                            pass
                else:
                    logger.warning(f"⚠️ 未获取到有效编号，文件: {file_path.name}")
                    continue  # 跳过当前文件，继续处理下一个
                    
            return generated_numbers
            
        except Exception as e:
            logger.error(f"添加文件编号申请失败: {str(e)}")
            # 截图帮助调试
            try:
                self.driver.save_screenshot("add_files_error.png")
                logger.info("已保存错误截图: add_files_error.png")
            except:
                pass
            raise
            
    def submit_application(self):
        """提交申请 - 修复提交按钮点击失败问题"""
        try:
            logger.info("🚀 开始提交编号申请...")
            
            # 多种选择器尝试点击提交按钮
            submit_selectors = [
                # 最精确的选择器 - 基于HTML结构
                "//div[@class='el-drawer__footer']//div[@class='dialog-footer']//button[@class='el-button el-button--primary']",
                # 备用选择器1 - 通过span文本
                "//div[@class='el-drawer__footer']//button[.//span[contains(text(), '提交')]]",
                # 备用选择器2 - 通过按钮类型
                "//div[@class='el-drawer__footer']//button[@class='el-button el-button--primary']",
                # 备用选择器3 - 更宽泛的选择器
                ".el-drawer__footer .el-button--primary",
                # 备用选择器4 - 仅通过文本
                "//button[.//span[text()=' 提交 ']]",
                "//button[.//span[text()='提交']]"
            ]
            
            submit_clicked = False
            for i, selector in enumerate(submit_selectors):
                try:
                    logger.info(f"  📝 尝试提交按钮选择器 {i+1}: {selector[:50]}...")
                    
                    if selector.startswith("//") or selector.startswith("/"):
                        submit_btn = WebDriverWait(self.driver, 8).until(EC.element_to_be_clickable(
                            (By.XPATH, selector)
                        ))
                    else:
                        submit_btn = WebDriverWait(self.driver, 8).until(EC.element_to_be_clickable(
                            (By.CSS_SELECTOR, selector)
                        ))
                    
                    # 确保按钮可见并可点击
                    if submit_btn.is_displayed() and submit_btn.is_enabled():
                        # 滚动到按钮位置
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
                        time.sleep(0.5)
                        
                        # 点击按钮
                        submit_btn.click()
                        logger.info(f"  ✅ 成功使用选择器 {i+1} 点击提交按钮")
                        submit_clicked = True
                        break
                    else:
                        logger.info(f"  ⚠️ 选择器 {i+1} 找到按钮但按钮不可用")
                        
                except TimeoutException:
                    logger.info(f"  ⚠️ 选择器 {i+1} 超时，尝试下一个...")
                    continue
                except Exception as e:
                    logger.info(f"  ⚠️ 选择器 {i+1} 出错: {str(e)}")
                    continue
            
            if not submit_clicked:
                # 最后尝试：寻找所有可能的提交按钮
                logger.info("  🔍 所有选择器失败，尝试寻找所有按钮...")
                try:
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for btn in all_buttons:
                        try:
                            btn_text = btn.text.strip()
                            if "提交" in btn_text and btn.is_displayed() and btn.is_enabled():
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", btn)
                                time.sleep(0.5)
                                btn.click()
                                logger.info(f"  ✅ 通过遍历按钮找到并点击提交按钮: '{btn_text}'")
                                submit_clicked = True
                                break
                        except:
                            continue
                except Exception as e:
                    logger.error(f"  ❌ 遍历按钮方式也失败: {str(e)}")
            
            if submit_clicked:
                logger.info("🎉 编号申请提交成功！")
                time.sleep(3)  # 等待提交处理
                
                # 检查是否有成功提示或新的弹窗
                try:
                    # 等待可能的成功提示
                    success_elements = [
                        ".el-message--success",
                        ".el-notification--success", 
                        "//div[contains(text(), '成功')]",
                        "//div[contains(text(), '申请成功')]"
                    ]
                    
                    for selector in success_elements:
                        try:
                            if selector.startswith("//"):
                                element = WebDriverWait(self.driver, 3).until(EC.presence_of_element_located(
                                    (By.XPATH, selector)
                                ))
                            else:
                                element = WebDriverWait(self.driver, 3).until(EC.presence_of_element_located(
                                    (By.CSS_SELECTOR, selector)
                                ))
                            logger.info(f"  📢 发现成功提示: {element.text[:50]}...")
                            break
                        except TimeoutException:
                            continue
                except:
                    pass
                    
                # 关闭模态框（如果仍然打开）
                try:
                    close_btn = WebDriverWait(self.driver, 2).until(EC.element_to_be_clickable(
                        (By.CLASS_NAME, "el-drawer__close-btn")
                    ))
                    close_btn.click()
                    logger.info("  📝 已关闭申请编号模态框")
                except TimeoutException:
                    logger.info("  📝 模态框可能已自动关闭")
                except Exception as e:
                    logger.info(f"  ⚠️ 关闭模态框时出现问题: {str(e)}")
                    
            else:
                raise Exception("❌ 所有提交按钮选择器都失败了")
            
        except Exception as e:
            logger.error(f"❌ 提交申请失败: {str(e)}")
            # 保存错误截图
            try:
                self.driver.save_screenshot("submit_error.png")
                logger.info("📸 已保存提交错误截图: submit_error.png")
            except:
                pass
            raise
            
    def process_file_type(self, doc_type, files):
        """处理特定类型的文件申请编号 - 提交后立即保存文件并返回主页"""
        if not files:
            logger.info(f"没有 {doc_type} 类型的文件需要处理")
            return {}
            
        logger.info(f"🔄 开始处理 {doc_type} 类型的 {len(files)} 个文件")
        
        try:
            # 重新打开申请编号模态框
            self.navigate_to_apply_number()
            
            # 填写基本信息
            self.fill_basic_info(doc_type)
            
            # 添加文件并生成编号
            generated_numbers = self.add_files_for_numbering(files)
            
            # 提交申请
            self.submit_application()
            
            # 【关键修改】提交成功后立即保存文件到本地，防止程序故障导致编号丢失
            if generated_numbers:
                logger.info(f"🔒 {doc_type} 类型提交成功，立即保存 {len(generated_numbers)} 个文件到本地...")
                self.update_files_with_numbers(generated_numbers)
                logger.info(f"✅ {doc_type} 类型文件已安全保存到 output_files 文件夹")
            
            # 【关键修改】返回主页面，准备处理下一种类型
            self.return_to_main_page()
            
            return generated_numbers
            
        except Exception as e:
            logger.error(f"❌ 处理 {doc_type} 类型文件失败: {str(e)}")
            # 即使出错也尝试返回主页面
            try:
                self.return_to_main_page()
            except:
                pass
            return {}
            
    def update_files_with_numbers(self, all_generated_numbers):
        """用生成的编号更新文件名和内容 - 输出到output_files文件夹"""
        try:
            logger.info("📁 开始更新文件名和内容并输出到output_files文件夹...")
            
            # 确保输出文件夹存在
            self.output_folder.mkdir(parents=True, exist_ok=True)
            
            updated_count = 0
            for original_name, number in all_generated_numbers.items():
                input_file_path = self.input_folder / original_name
                if not input_file_path.exists():
                    logger.warning(f"⚠️ 输入文件不存在: {input_file_path}")
                    continue
                    
                # 创建新的文件名（包含编号）
                new_name = f"{number}-{original_name}"
                output_file_path = self.output_folder / new_name
                
                try:
                    # 复制文件到输出文件夹
                    import shutil
                    shutil.copy2(input_file_path, output_file_path)
                    logger.info(f"  📄 文件复制: {original_name} -> {new_name}")
                    
                    # 更新文件内容（添加编号）
                    from file_number_filler import update_document
                    update_document(output_file_path, number)
                    logger.info(f"  ✅ 文件内容已更新: {new_name}")
                    updated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ 更新文件失败 {original_name}: {str(e)}")
                    
            logger.info(f"🎉 文件更新完成! 成功处理 {updated_count}/{len(all_generated_numbers)} 个文件")
            logger.info(f"📂 输出文件夹: {self.output_folder.absolute()}")
            
        except Exception as e:
            logger.error(f"❌ 更新文件过程失败: {str(e)}")
            raise
            
    def return_to_main_page(self):
        """返回主页面，准备处理下一种类型的文档申请"""
        try:
            logger.info("🏠 返回主页面，准备处理下一种类型...")
            
            # 提交后模态框已自动消失，直接导航到主页URL即可
            try:
                logger.info(f"  📍 导航到主页: {self.base_url}")
                self.driver.get(self.base_url)
                time.sleep(3)  # 等待页面加载
                
                # 导航到主页后需要点击登录按钮（不用输入账号密码）
                logger.info("  🔑 点击登录按钮...")
                try:
                    login_btn = WebDriverWait(self.driver, 8).until(EC.element_to_be_clickable(
                        (By.XPATH, "//button[contains(text(), '登录') or contains(text(), '登陆')]")
                    ))
                    login_btn.click()
                    logger.info("  ✅ 成功点击登录按钮")
                    time.sleep(3)  # 等待3秒让页面稳定
                except TimeoutException:
                    # 备用选择器
                    try:
                        login_btn = WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(
                            (By.CSS_SELECTOR, "button[type='submit'], .login-btn, .el-button--primary")
                        ))
                        login_btn.click()
                        logger.info("  ✅ 使用备用选择器点击登录按钮")
                        time.sleep(3)
                    except TimeoutException:
                        logger.info("  📝 未找到登录按钮或已经登录状态")
                
                # 检查是否成功返回主页（寻找申请编号按钮）
                try:
                    WebDriverWait(self.driver, 8).until(EC.presence_of_element_located(
                        (By.XPATH, "//span[contains(text(), '申请编号')]")
                    ))
                    logger.info("  ✅ 成功返回主页面，找到申请编号按钮")
                    return
                except TimeoutException:
                    logger.info("  ⚠️ 主页加载后未找到申请编号按钮，可能需要重新关闭弹窗")
                    # 尝试关闭可能的弹窗
                    self.close_popups()
                    return
                    
            except Exception as e:
                logger.warning(f"  ⚠️ 导航主页失败: {str(e)}")
            
            # 最后检查是否回到了主页面
            try:
                WebDriverWait(self.driver, 3).until(EC.presence_of_element_located(
                    (By.XPATH, "//span[contains(text(), '申请编号')]")
                ))
                logger.info("  🎉 确认已返回主页面")
            except TimeoutException:
                logger.warning("  ⚠️ 无法确认是否已返回主页面，但继续执行...")
                
        except Exception as e:
            logger.error(f"❌ 返回主页面失败: {str(e)}")
            # 不抛出异常，继续执行后续流程
            
    def run(self):
        """主运行方法"""
        try:
            # 检查必要信息是否填写
            if not self.username or not self.password or not self.project_code:
                logger.error("配置信息不完整!")
                logger.error("请在config.py文件中填写以下信息:")
                logger.error("- USERNAME: 用户名")
                logger.error("- PASSWORD: 密码") 
                logger.error("- PROJECT_CODE: 车型代号")
                raise Exception("配置信息不完整，请检查config.py文件")
                    
            logger.info(f"开始执行申请编号程序...")
            logger.info(f"用户名: {self.username}")
            logger.info(f"项目代号: {self.project_code}")
            
            # 设置浏览器
            self.setup_driver()
            
            # 登录
            self.login()
            
            # 关闭弹窗
            self.close_popups()
            
            # 获取文件列表
            files_by_type = self.get_files_by_type()
            
            if not any(files_by_type.values()):
                logger.warning("输入文件夹中没有找到需要处理的文件")
                return
                
            all_generated_numbers = {}
            
            # 按类型处理文件 - 每种类型处理完后立即保存到本地
            for doc_type in ["DVP", "PPL", "FN"]:
                if files_by_type[doc_type]:
                    logger.info(f"🔄 准备处理 {doc_type} 类型的 {len(files_by_type[doc_type])} 个文件")
                    numbers = self.process_file_type(doc_type, files_by_type[doc_type])
                    all_generated_numbers.update(numbers)
                    
                    # 如果有编号生成，立即显示结果
                    if numbers:
                        logger.info(f"✅ {doc_type} 类型处理完成，生成编号:")
                        for filename, number in numbers.items():
                            logger.info(f"  📄 {filename} -> {number}")
                    
            # 最终汇总
            if all_generated_numbers:
                logger.info(f"🎉 申请编号程序执行完成！总共处理了 {len(all_generated_numbers)} 个文件")
                logger.info(f"📂 所有文件已保存到: {self.output_folder.absolute()}")
                print("\n" + "="*60)
                print("           🎉 申请编号完成！")
                print("="*60)
                print("生成的编号汇总:")
                for filename, number in all_generated_numbers.items():
                    print(f"  📄 {filename} -> {number}")
                print(f"\n📂 输出位置: {self.output_folder.absolute()}")
                print("="*60)
            else:
                logger.warning("⚠️ 没有成功生成任何编号")
                print("⚠️ 没有成功生成任何编号，请检查输入文件或网络连接")
                
        except Exception as e:
            logger.error(f"程序执行失败: {str(e)}")
            raise
        finally:
            if self.driver:
                logger.info("🔄 程序执行完成，正在关闭浏览器...")
                self.driver.quit()
                logger.info("✅ 浏览器已关闭")


if __name__ == "__main__":
    app = ApplyIDAutomation()
    app.run()
