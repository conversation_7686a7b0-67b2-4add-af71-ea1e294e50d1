import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFileDialog, QLineEdit, QGroupBox, QMessageBox, QComboBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class RTEGUI(QMainWindow):
    def __init__(self, on_home=None, on_back=None):
        super().__init__()
        self.on_home = on_home
        self.on_back = on_back
        self.setWindowTitle("RTE点检工具")
        self.setGeometry(240, 160, 700, 500)
        self.init_ui()
        self.setup_styles()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 10, 20, 20)
        
        # 顶部导航
        nav_container = self.create_nav_bar()
        main_layout.addWidget(nav_container)
        
        # 标题
        title_label = QLabel("RTE点检工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0 20px 0;")
        main_layout.addWidget(title_label)
        
        # 配置区域
        config_group = self.create_config_group()
        main_layout.addWidget(config_group)
        
        # 操作按钮区域
        button_layout = self.create_button_layout()
        main_layout.addLayout(button_layout)
        
        # 状态提示
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("Arial", 11))
        self.status_label.setStyleSheet("color: #27ae60; margin: 10px 0; padding: 5px;")
        main_layout.addWidget(self.status_label)
        
        main_layout.addStretch()

    def create_nav_bar(self):
        """创建导航栏"""
        nav_container = QWidget()
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 10)
        # 返回上一级按钮（橙色）
        back_btn = QPushButton("返回上一级")
        back_btn.setFont(QFont("Arial", 11))
        back_btn.setFixedSize(120, 36)
        if self.on_back:
            back_btn.clicked.connect(self.on_back)
        else:
            back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        nav_layout.addWidget(back_btn)
        # 返回主页按钮（主色蓝色）
        home_btn = QPushButton("返回主页")
        home_btn.setFont(QFont("Arial", 11))
        home_btn.setFixedSize(100, 32)
        if self.on_home:
            home_btn.clicked.connect(self.on_home)
        else:
            home_btn.clicked.connect(self.close)
        home_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        nav_layout.addWidget(home_btn)
        nav_layout.addStretch()
        
        return nav_container

    def create_config_group(self):
        """创建配置分组"""
        group = QGroupBox("RTE点检配置")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QGridLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 控制器型号
        layout.addWidget(QLabel("控制器型号："), 0, 0)
        self.ctrl_combo = QComboBox()
        self.ctrl_combo.setFont(QFont("Arial", 11))
        self.ctrl_combo.addItems(["D2", "D3", "OB", "域控"])
        layout.addWidget(self.ctrl_combo, 0, 1)
        
        # 实车数据文件夹
        layout.addWidget(QLabel("实车数据文件夹："), 1, 0)
        self.data_dir_edit = QLineEdit()
        self.data_dir_edit.setFont(QFont("Arial", 11))
        self.data_dir_edit.setReadOnly(True)
        self.data_dir_edit.setPlaceholderText("请选择实车数据文件夹...")
        layout.addWidget(self.data_dir_edit, 1, 1)
        
        data_browse_btn = QPushButton("浏览")
        data_browse_btn.setFont(QFont("Arial", 11))
        data_browse_btn.setFixedSize(80, 32)
        data_browse_btn.clicked.connect(self.browse_data_dir)
        data_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(data_browse_btn, 1, 2)
        
        # 输出结果保存文件夹
        layout.addWidget(QLabel("输出结果保存文件夹："), 2, 0)
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setFont(QFont("Arial", 11))
        self.output_dir_edit.setReadOnly(True)
        self.output_dir_edit.setPlaceholderText("请选择输出结果保存文件夹...")
        layout.addWidget(self.output_dir_edit, 2, 1)
        
        output_browse_btn = QPushButton("浏览")
        output_browse_btn.setFont(QFont("Arial", 11))
        output_browse_btn.setFixedSize(80, 32)
        output_browse_btn.clicked.connect(self.browse_output_dir)
        output_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(output_browse_btn, 2, 2)
        
        # elf文件保存地址
        layout.addWidget(QLabel("elf文件保存地址："), 3, 0)
        self.elf_dir_edit = QLineEdit()
        self.elf_dir_edit.setFont(QFont("Arial", 11))
        self.elf_dir_edit.setReadOnly(True)
        self.elf_dir_edit.setPlaceholderText("请选择elf文件保存地址...")
        layout.addWidget(self.elf_dir_edit, 3, 1)
        
        elf_browse_btn = QPushButton("浏览")
        elf_browse_btn.setFont(QFont("Arial", 11))
        elf_browse_btn.setFixedSize(80, 32)
        elf_browse_btn.clicked.connect(self.browse_elf_dir)
        elf_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(elf_browse_btn, 3, 2)
        
        return group

    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # 确定按钮
        self.ok_btn = QPushButton("开始点检")
        self.ok_btn.setFont(QFont("Arial", 11, QFont.Bold))
        self.ok_btn.setFixedSize(120, 36)
        self.ok_btn.clicked.connect(self.run_rte)
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #1abc9c;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #16a085;
            }
        """)
        layout.addWidget(self.ok_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setFont(QFont("Arial", 11))
        self.cancel_btn.setFixedSize(100, 36)
        self.cancel_btn.clicked.connect(self.cancel_rte)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(self.cancel_btn)
        
        return layout

    def setup_styles(self):
        """设置整体样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0;
                padding-top: 15px;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: #ecf0f1;
            }
            QLabel {
                color: #2c3e50;
                font-size: 11pt;
                font-weight: normal;
            }
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px;
                font-size: 11pt;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px;
                font-size: 11pt;
                background-color: white;
            }
        """)

    def browse_data_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择实车数据文件夹")
        if path:
            self.data_dir_edit.setText(path)

    def browse_output_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择输出结果保存文件夹")
        if path:
            self.output_dir_edit.setText(path)

    def browse_elf_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择elf文件保存地址")
        if path:
            self.elf_dir_edit.setText(path)

    def run_rte(self):
        # 这里应调用实际RTE点检程序
        self.status_label.setText("✓ RTE点检已完成！")
        QMessageBox.information(self, "提示", "RTE点检已完成！")

    def cancel_rte(self):
        # 这里应实现取消功能（暂未实现）
        self.status_label.setText("已取消（未实现实际停止功能）")
        QMessageBox.information(self, "提示", "已取消（未实现实际停止功能）")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    window = RTEGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
