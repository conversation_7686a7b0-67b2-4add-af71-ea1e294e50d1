# VSETools 1.0 集成开发GUI

## 项目概述

VSETools 1.0 是一个集成多个子功能的GUI应用程序，使用PyQt5开发，旨在为技术团队提供统一的工具入口。

## 功能模块

### 1. 自动文件处理 (Apply_Fillin_Upload)
- 集成了Apply_Fillin_Upload\main_gui_final.py的完整功能
- 车型文件管理系统
- 文件申请编号、填写、上传等自动化处理

### 2. 自动软件开发
包含以下子模块：

#### 2.1 软件标定 (AutoCali)
- 基于AutoCali\AutoCali.py的功能
- 提供PyQt5版本的现代化界面
- 支持BLF/ASC文件解析
- MAT文件转换
- CSE/SSE标定功能

#### 2.2 软件集成 (开发中)
- 预留接口，未来扩展功能

#### 2.3 DM制作 (DMUtils)
- 预留接口，未来扩展功能

#### 2.4 RTE点检 (RTETestUtils)
- 预留接口，未来扩展功能

## 系统要求

- Python 3.7+
- Windows 10/11 (推荐)
- 2GB RAM 以上
- 500MB 可用磁盘空间

## 安装和运行

### 方法1：使用启动脚本（推荐）
1. 双击运行 `启动VSETools.bat`
2. 脚本会自动检查Python环境和依赖包
3. 如果缺少依赖包，会自动安装

### 方法2：手动安装
1. 确保已安装Python 3.7+
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行主程序：
   ```bash
   python main_gui.py
   ```

## 使用说明

### 主界面
- 启动后显示VSETools 1.0主页面
- 包含两个主要入口按钮：
  - **自动文件处理**: 进入车型文件管理系统
  - **自动软件开发**: 进入软件开发工具集

### 导航
- 所有页面都在一个窗口内切换，避免多窗口混乱
- 每个子页面都有导航按钮：
  - **返回主页**: 直接回到主界面
  - **返回上一级**: 返回到上级菜单（如适用）

### 自动文件处理
- 使用Apply_Fillin_Upload的完整功能
- 支持车型文件管理、申请编号、自动填写等

### 软件标定
- 支持文件夹管理（输入/输出）
- 数据处理流程：
  1. 解析BLF/ASC文件
  2. 运行MAT转换
  3. 转换为CSV格式
  4. 编译软件
  5. 运行CSE/SSE标定

## 项目结构

```
VSETools/
├── main_gui.py                    # 主GUI入口文件
├── requirements.txt               # 依赖包列表
├── 启动VSETools.bat              # Windows启动脚本
├── README.md                     # 本文档
├── Apply_Fillin_Upload/          # 自动文件处理模块
│   ├── main_gui_final.py        # 车型文件管理系统
│   └── ...
├── AutoCali/                     # 软件标定模块
│   ├── AutoCali.py             # 原始tkinter版本
│   ├── AutoCali_GUI.py         # PyQt5版本
│   └── ...
├── DMUtils/                      # DM制作模块
├── RTETestUtils/                 # RTE点检模块
└── ...
```

## 开发说明

### 扩展新功能
1. 在相应的模块文件夹中创建PyQt5版本的GUI
2. 在main_gui.py中添加导入和页面创建代码
3. 添加导航按钮连接

### 样式规范
- 使用统一的颜色方案和字体
- 保持界面简洁美观
- 适合技术团队使用习惯

## 注意事项

1. 首次运行可能需要时间安装依赖包
2. 某些功能需要特定的文件路径和环境配置
3. 建议在使用前先阅读各子模块的具体说明文档

## 故障排除

### 常见问题
1. **无法启动**: 检查Python环境和依赖包
2. **模块导入失败**: 确保所有子模块文件存在
3. **界面异常**: 检查PyQt5版本是否正确

### 日志查看
- 程序运行日志会保存在logs目录
- 可通过日志监控选项卡查看实时日志

## 更新日志

### v1.0.0
- 初始版本
- 集成Apply_Fillin_Upload功能
- 实现AutoCali的PyQt5版本
- 提供统一的主界面

## 联系方式

如有问题或建议，请联系开发团队。
