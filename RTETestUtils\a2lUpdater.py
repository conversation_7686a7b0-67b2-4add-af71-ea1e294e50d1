#!/usr/bin/env python
import re
import subprocess
import os
import sys

#################################################
##   Dwarf parsing...
#################################################

dwarfArray = []
symTab = {}


def parseSymbolTable(elfFileName):

def parseDwarfOutput(elfFileName):

def getDwarfType(typeAddress):


def getDwarfVar(name):


def printDwarfVar(FoundType, baseAddr, name):


def findAddress(name, useSymbolTable=False):


#################################################
##   A2L parsing...
#################################################


a2lInput = ""
trenner = [' ', '[', ']', '(', ')', ',', ';', '=', '\r', '\n']


def getNextToken(pos, length):


def updateA2L(fileName, useSymbolTable=False):



def updateA2lByElf(elfname, a2lname):

