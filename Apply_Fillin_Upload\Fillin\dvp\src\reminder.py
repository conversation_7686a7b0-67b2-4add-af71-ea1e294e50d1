import tkinter as tk
from tkinter import messagebox
from src.logger_setup import setup_logger

logger = setup_logger()


def show_reminder(enable_popup: bool, enable_console: bool):
    """显示提醒，提示用户手动填写计划完成时间"""
    reminder_message = "请手动在DVP文件计划完成时间部分，在合适的单元格里填写小红原点和计划时间（时间段）。"

    if enable_console:
        logger.info(f"提醒: {reminder_message}")

    if enable_popup:
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showinfo("提醒", reminder_message)
            root.destroy()
        except Exception as e:
            logger.error(f"弹窗提醒失败: {str(e)}")
            if enable_console:
                logger.info(f"弹窗失败，控制台提醒: {reminder_message}")