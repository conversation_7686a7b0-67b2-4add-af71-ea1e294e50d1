from datetime import datetime
from pathlib import Path
from docx import Document
from docx.oxml.ns import qn
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import logging

logger = logging.getLogger(__name__)


def get_current_date() -> tuple:
    """
    获取当前年、月、日。

    返回：
        tuple: (年, 月, 日)，均为字符串格式。
    """
    now = datetime.now()
    return str(now.year), str(now.month).zfill(2), str(now.day).zfill(2)


def modify_first_page(doc: Document, config: dict) -> None:
    """
    修改文档第一页的内容。

    参数：
        doc (Document): python-docx 的 Document 对象。
        config (dict): 包含车型代号和版本号的配置数据。

    异常：
        Exception: 如果修改过程失败。
    """
    try:
        year, month, day = get_current_date()

        # 访问第一页的段落
        paragraphs = doc.paragraphs

        # 修改车型代号，保留格式
        project_found = False
        for para in paragraphs:
            if '项目' in para.text:
                for run in para.runs:
                    if '项目' in run.text:
                        run.text = run.text.replace(run.text.split('项目')[0], config['vehicle_model_code'])
                        project_found = True
                        logger.info("已将第一页的项目代码修改为车型代号，保留格式")
                        break
                if project_found:
                    break
        if not project_found:
            logger.warning("第一页未找到‘项目’")

        # 修改版本号，设置为 Times New Roman 18 号
        version_found = False
        for i, para in enumerate(paragraphs):
            if '版本号' in para.text:
                if i + 1 < len(paragraphs):
                    target_para = paragraphs[i + 1]
                    target_para.clear()
                    run = target_para.add_run(config['version_number'])
                    run.font.name = 'Times New Roman'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')  # 兼容中文
                    run.font.size = Pt(18)
                    version_found = True
                    logger.info("已修改第一页的版本号，设置为 Times New Roman 18 号")
                else:
                    logger.warning("‘版本号’后未找到可修改的段落")
                break
        if not version_found:
            logger.warning("第一页未找到‘版本号’")

        # 修改日期，搜索包含“年”“月”“日”的行
        date_modified = False
        for para in paragraphs:
            if '年' in para.text and '月' in para.text and '日' in para.text:
                para.clear()
                # 按顺序添加日期部分，分别设置字体
                parts = [
                    (year, 'Times New Roman'),  # 年份
                    ('年', '宋体'),
                    (month, 'Times New Roman'),  # 月份
                    ('月', '宋体'),
                    (day, 'Times New Roman'),  # 日期
                    ('日', '宋体')
                ]
                for text, font_name in parts:
                    run = para.add_run(text)
                    run.font.name = font_name
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')  # 兼容中文
                    run.font.size = Pt(18)
                    run.font.bold = True
                para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                date_modified = True
                logger.info(
                    f"已修改第一页的日期为 {year}年{month}月{day}日，数字为 Times New Roman 18 号，汉字为宋体 18 号，居中")
                break
        if not date_modified:
            logger.warning("第一页未找到包含‘年月日’的日期行")

    except Exception as e:
        logger.error(f"修改第一页时出错：{e}")
        raise


def modify_second_page(doc: Document, config: dict) -> None:
    """
    修改第二页的页眉和表格内容。

    参数：
        doc (Document): python-docx 的 Document 对象。
        config (dict): 配置数据。

    异常：
        IndexError: 如果未找到预期表格或单元格。
    """
    try:
        year, month, day = get_current_date()
        date_str = f"{year}/{month}/{day}"

        # 修改第二页及之后页的页眉表格
        for section_idx, section in enumerate(doc.sections[1:], start=2):
            header = section.header
            if not header.tables:
                logger.warning(f"第{section_idx}页页眉未找到表格")
                continue
            for table_idx, table in enumerate(header.tables):
                for row_idx, row in enumerate(table.rows):
                    try:
                        for cell_idx, cell in enumerate(row.cells):
                            cell_text = cell.text.strip()
                            if '文件名称' in cell_text:
                                if cell_idx + 1 < len(row.cells):
                                    target_cell = row.cells[cell_idx + 1]
                                    current_text = target_cell.text.strip()

                                    # 如果单元格包含"XX项目"格式的内容，替换XX为车型代号
                                    if 'XX项目' in current_text:
                                        new_text = current_text.replace('XX项目', f"{config['vehicle_model_code']}项目")
                                        target_cell.text = new_text
                                        for para in target_cell.paragraphs:
                                            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                        logger.info(f"第{section_idx}页页眉表格{table_idx}已将XX替换为车型代号并居中")
                                    # 其他情况一律填写车型代号接口定义通知单
                                    else:
                                        target_cell.text = f"{config['vehicle_model_code']}接口定义通知单"
                                        for para in target_cell.paragraphs:
                                            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                        logger.info(
                                            f"第{section_idx}页页眉表格{table_idx}已修改为车型代号接口定义通知单并居中")
                                else:
                                    logger.warning(
                                        f"第{section_idx}页页眉表格{table_idx}行{row_idx}无右侧单元格（文件名称）")
                            if '版次/修订状态' in cell_text:
                                if cell_idx + 1 < len(row.cells):
                                    target_cell = row.cells[cell_idx + 1]
                                    target_cell.text = config['version_number']
                                    for para in target_cell.paragraphs:
                                        para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                    logger.info(f"第{section_idx}页页眉表格{table_idx}已修改版本号并居中")
                                else:
                                    logger.warning(
                                        f"第{section_idx}页页眉表格{table_idx}行{row_idx}无右侧单元格（版次/修订状态）")
                    except Exception as e:
                        logger.error(f"处理第{section_idx}页页眉表格{table_idx}行{row_idx}时出错：{e}")
                        continue

        # 修改正文中的第一个含空白单元格的表格行
        tables = doc.tables
        if not tables:
            logger.error("文档中未找到表格")
            raise IndexError("未找到表格")

        for table_idx, table in enumerate(tables):
            for row_idx, row in enumerate(table.rows):
                try:
                    has_blank = any(cell.text.strip() == '' for cell in row.cells)
                    if has_blank and len(row.cells) >= 6:
                        # 填充并设置格式
                        cells_content = [
                            (0, config['version_number']),
                            (1, ''),
                            (2, date_str),
                            (3, config['vehicle_manager']),
                            (4, config['application_manager']),
                            (5, config['division_manager'])
                        ]
                        for cell_idx, content in cells_content:
                            cell = row.cells[cell_idx]
                            cell.text = content
                            for para in cell.paragraphs:
                                para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                for run in para.runs:
                                    run.font.size = Pt(12)
                                    run.font.name = 'Times New Roman'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                        logger.info(f"已填充表格{table_idx}行{row_idx}（第二页正文），设置 12 号字体并居中")
                        return
                except Exception as e:
                    logger.error(f"处理表格{table_idx}行{row_idx}时出错：{e}")
                    continue
        logger.warning("未找到含空白单元格的表格行（第二页正文）")

    except Exception as e:
        logger.error(f"修改第二页时出错：{e}")
        raise


def modify_third_page(doc: Document, config: dict) -> None:
    """
    修改第三页表格内容。

    参数：
        doc (Document): python-docx 的 Document 对象。
        config (dict): 配置数据。

    异常：
        IndexError: 如果未找到预期表格或单元格。
    """
    try:
        year, month, day = get_current_date()
        date_str = f"{year}/{month}/{day}"

        # 查找第三页的表格
        tables = doc.tables
        if len(tables) < 2:
            logger.error("第三页的表格数量不足")
            raise IndexError("表格数量不足")

        # 跟踪填充状态
        filled_composed = False
        filled_reviewed = False
        filled_approved = False

        # 处理“编制”“审核”“批准”
        for table_idx, table in enumerate(tables[1:], start=1):
            for row_idx, row in enumerate(table.rows):
                try:
                    for cell_idx, cell in enumerate(row.cells):
                        cell_text = cell.text.strip()
                        if '编制' in cell_text and not filled_composed:
                            if cell_idx + 1 < len(row.cells):
                                target_cell = row.cells[cell_idx + 1]
                                target_cell.text = config['vehicle_manager']
                                for para in target_cell.paragraphs:
                                    para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                    for run in para.runs:
                                        run.font.size = Pt(12)
                                        run.font.name = 'Times New Roman'
                                        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                filled_composed = True
                                logger.info(f"表格{table_idx}已填充‘编制’单元格，设置 12 号字体并居中")
                            else:
                                logger.warning(f"表格{table_idx}行{row_idx}无右侧单元格（编制）")
                        if '审核' in cell_text and not filled_reviewed:
                            if cell_idx + 1 < len(row.cells):
                                target_cell = row.cells[cell_idx + 1]
                                target_cell.text = config['application_manager']
                                for para in target_cell.paragraphs:
                                    para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                    for run in para.runs:
                                        run.font.size = Pt(12)
                                        run.font.name = 'Times New Roman'
                                        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                filled_reviewed = True
                                logger.info(f"表格{table_idx}已填充‘审核’单元格，设置 12 号字体并居中")
                            else:
                                logger.warning(f"表格{table_idx}行{row_idx}无右侧单元格（审核）")
                        if '批准' in cell_text and not filled_approved:
                            if cell_idx + 1 < len(row.cells):
                                target_cell = row.cells[cell_idx + 1]
                                target_cell.text = config['division_manager']
                                for para in target_cell.paragraphs:
                                    para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                    for run in para.runs:
                                        run.font.size = Pt(12)
                                        run.font.name = 'Times New Roman'
                                        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                filled_approved = True
                                logger.info(f"表格{table_idx}已填充‘批准’单元格，设置 12 号字体并居中")
                            else:
                                logger.warning(f"表格{table_idx}行{row_idx}无右侧单元格（批准）")
                except Exception as e:
                    logger.error(f"处理表格{table_idx}行{row_idx}时出错：{e}")
                    continue

        # 处理“日期”单元格
        date_count = 0
        for table_idx, table in enumerate(tables[1:], start=1):
            for row_idx, row in enumerate(table.rows):
                try:
                    for cell_idx, cell in enumerate(row.cells):
                        cell_text = cell.text.strip()
                        if '日期' in cell_text and date_count < 3:
                            if cell_idx + 1 < len(row.cells):
                                target_cell = row.cells[cell_idx + 1]
                                target_cell.text = date_str
                                for para in target_cell.paragraphs:
                                    para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                    for run in para.runs:
                                        run.font.size = Pt(12)
                                        run.font.name = 'Times New Roman'
                                        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                date_count += 1
                                logger.info(
                                    f"表格{table_idx}已填充‘日期’右侧单元格（{date_count}/3），设置为 Times New Roman 12 号")
                            else:
                                logger.warning(f"表格{table_idx}行{row_idx}无右侧单元格（日期）")
                except Exception as e:
                    logger.error(f"处理表格{table_idx}行{row_idx}时出错：{e}")
                    continue
        if date_count < 3:
            logger.warning(f"仅找到 {date_count} 个‘日期’单元格，未达预期 3 个")

    except Exception as e:
        logger.error(f"修改第三页时出错：{e}")
        raise


def process_document(input_path: str, output_dir: Path, config: dict) -> None:
    """
    处理单个 .docx 文件并保存修改后的版本。

    参数：
        input_path (str): 输入 .docx 文件路径。
        output_dir (Path): 输出文件保存目录。
        config (dict): 配置数据。

    异常：
        Exception: 如果处理失败。
    """
    try:
        logger.info(f"正在处理文件：{input_path}")
        doc = Document(input_path)

        # 应用修改
        modify_first_page(doc, config)
        modify_second_page(doc, config)
        modify_third_page(doc, config)

        # 保存修改后的文档，使用原始文件名
        output_filename = Path(input_path).name
        output_path = output_dir / output_filename
        doc.save(output_path)
        logger.info(f"已保存修改后的文件：{output_path}")

    except Exception as e:
        logger.error(f"处理文件 {input_path} 失败：{e}")
        raise