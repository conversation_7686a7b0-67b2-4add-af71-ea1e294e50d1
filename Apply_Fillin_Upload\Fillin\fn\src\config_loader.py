import pandas as pd
import re
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)


def load_config_from_excel() -> dict:
    """
    从 Excel 文件加载配置数据。

    返回：
        dict: 配置数据，包含 vehicle_model_code, vehicle_manager, application_manager, division_manager, version_number。

    异常：
        FileNotFoundError: 如果 Excel 文件未找到。
        KeyError: 如果缺少必需的列或角色。
    """
    try:
        # 计算 Fill_Template_Data.xlsx 的绝对路径
        excel_path = os.path.abspath(
            os.path.join(
                os.path.dirname(__file__),  # fn/src
                "..",  # fn
                "..",  # 父目录
                "..",  # 更上一级目录
                "Fill_Template_Data.xlsx"
            )
        )
        logger.info(f"Excel 文件路径：{excel_path}")

        # 读取 Excel 文件的 Sheet1
        df = pd.read_excel(excel_path, sheet_name='Info')

        # 检查必需列
        required_columns = ['角色', 'role', 'people']
        if not all(col in df.columns for col in required_columns):
            missing_cols = [col for col in required_columns if col not in df.columns]
            logger.error(f"Excel 文件缺少列：{missing_cols}")
            raise KeyError(f"缺少必需列：{missing_cols}")

        # 映射 role 到 config 字段
        role_mapping = {
            'VehicleCode': 'vehicle_model_code',
            'VehicleLead': 'vehicle_manager',
            'ApplicationManager': 'application_manager',
            'DivisionManager': 'division_manager'
        }

        config = {'version_number': 'V001'}  # 默认版本号
        for role, config_key in role_mapping.items():
            # 查找对应 role 的行
            row = df[df['role'] == role]
            if row.empty:
                logger.error(f"未找到角色：{role}")
                raise KeyError(f"未找到角色：{role}")

            # 获取 people 列的值
            people = row['people'].iloc[0]
            if pd.isna(people):
                logger.warning(f"角色 {role} 的 people 列为空")
                config[config_key] = ''
            else:
                # 处理多值情况，使用正则表达式分割
                people_list = re.split(r'[,，;\s]+', str(people).strip())
                people_list = [p for p in people_list if p]  # 移除空字符串
                config[config_key] = ', '.join(people_list)
                logger.info(f"角色 {role} 的值：{config[config_key]}")

        logger.info("从 Excel 加载配置成功")
        return config

    except FileNotFoundError:
        logger.error(f"未找到 Excel 文件：{excel_path}")
        raise
    except Exception as e:
        logger.error(f"加载 Excel 配置失败：{e}")
        raise