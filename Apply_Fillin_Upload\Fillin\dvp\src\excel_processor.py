import openpyxl
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import re
from typing import Optional, List
from src.logger_setup import setup_logger
from src.config import Config
import os

logger = setup_logger()

class ExcelProcessor:
    """处理 Excel 文件，填充软件设计验证计划内容"""

    def __init__(self, file_path: str, enable_popup: bool = True, enable_console_reminder: bool = True):
        self.file_path = file_path
        self.wb = None
        self.missed_fields: List[str] = []
        self.enable_popup = enable_popup
        self.enable_console_reminder = enable_console_reminder
        self.config = Config()

    def load_workbook(self) -> bool:
        try:
            if not os.path.exists(self.file_path):
                logger.error(f"文件 {self.file_path} 不存在")
                return False
            if not self.file_path.endswith('.xlsx'):
                logger.error("仅支持 .xlsx 格式文件")
                return False
            self.wb = load_workbook(self.file_path)
            logger.info(f"成功加载文件: {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"加载文件失败: {str(e)}")
            return False

    def clean_sheets(self) -> bool:
        try:
            sheets_to_remove = [s for s in self.wb.sheetnames if "履历" in s or "使用说明" in s]
            for sheet in sheets_to_remove:
                del self.wb[sheet]
                logger.info(f"已删除 sheet: {sheet}")
            if len(self.wb.sheetnames) != 2:
                logger.error(f"文件包含 {len(self.wb.sheetnames)} 个 sheet，预期为 2 个")
                return False
            logger.info("sheet 数量验证通过")
            return True
        except Exception as e:
            logger.error(f"清理 sheet 失败: {str(e)}")
            return False

    def find_plan_sheet(self) -> Optional[openpyxl.worksheet.worksheet.Worksheet]:
        for sheet in self.wb:
            if "验证计划" in sheet.title:
                logger.info(f"找到验证计划 sheet: {sheet.title}")
                return sheet
        logger.error("未找到包含 '验证计划' 的 sheet")
        return None

    def find_sample_sheet(self) -> Optional[openpyxl.worksheet.worksheet.Worksheet]:
        for sheet in self.wb:
            if "样车样件需求" in sheet.title:
                logger.info(f"找到样车样件需求 sheet: {sheet.title}")
                return sheet
        logger.error("未找到包含 '样车样件需求' 的 sheet")
        return None

    def update_plan_sheet(self, sheet):
        try:
            # 更新首行合并单元格
            for merged_range in sheet.merged_cells.ranges:
                if merged_range.start_cell.row == 1:
                    cell = sheet[merged_range.start_cell.coordinate]
                    if cell.value and "项目" in cell.value:
                        cell.value = f"{self.config.VEHICLE_CODE}{cell.value[cell.value.find('项目'):]}"
                        logger.info("已更新首行合并单元格")
                    break

            fields = {
                r"软件责任工程师[:：]": (self.config.SOFTWARE_ENGINEER, True),
                r"核心小组成员[:：]": (self.config.CORE_TEAM, True),
                r"版本[:：]": (self.config.VERSION, True),
                r"日期[:：]": (self.config.CURRENT_DATE, True),
                r"编制[:：]": (self.config.COMPILER, False),
                r"校对[:：]": (self.config.REVIEWER, False),
                r"审核[:：]": (self.config.AUDITOR, False),
                r"会签[:：]": (self.config.COUNTERSIGNER, False),
                r"研发品质经理/主管[:：]": (self.config.QUALITY_MANAGER, False),
                r"批准[:：]": (self.config.APPROVER, False)
            }

            for row in sheet.iter_rows():
                for cell in row:
                    if not cell.value:
                        continue
                    for pattern, (value, fill_right) in fields.items():
                        if re.search(pattern, str(cell.value)):
                            try:
                                if fill_right:
                                    target_cell = sheet.cell(row=cell.row, column=cell.column + 1)
                                    target_cell.value = value
                                    logger.info(f"更新字段: {pattern} -> {value} at {target_cell.coordinate} (右侧)")
                                else:
                                    target_cell = cell
                                    colon_pos = str(cell.value).find("：") if "：" in str(cell.value) else str(cell.value).find(":")
                                    if colon_pos != -1:
                                        target_cell.value = str(cell.value)[:colon_pos + 1] + value
                                    else:
                                        target_cell.value = value
                                    logger.info(f"更新字段: {pattern} -> {value} at {target_cell.coordinate} (替换)")
                            except Exception as e:
                                logger.warning(f"无法更新单元格 {target_cell.coordinate}: {str(e)}")
                                self.missed_fields.append(f"字段 {pattern} 未更新: {str(e)}")
                            break

                    if cell.value == "试验方":
                        merge_row_span = 1
                        for merged_range in sheet.merged_cells.ranges:
                            if cell.coordinate in merged_range:
                                merge_row_span = merged_range.max_row - merged_range.min_row + 1
                                break
                        cell_below = sheet.cell(row=cell.row + merge_row_span, column=cell.column)
                        cell_below.value = self.config.DEPARTMENT
                        logger.info(f"更新试验方下方单元格: {self.config.DEPARTMENT} at {cell_below.coordinate}")
                        cell_below_below = sheet.cell(row=cell.row + merge_row_span + 1, column=cell.column)
                        if cell_below_below.value:
                            cell_below_below.value = self.config.DEPARTMENT
                            logger.info(f"更新试验方下下方单元格: {self.config.DEPARTMENT} at {cell_below_below.coordinate}")

        except Exception as e:
            logger.error(f"更新验证计划 sheet 失败: {str(e)}")
            self.missed_fields.append(f"验证计划 sheet 更新错误: {str(e)}")

    def update_sample_sheet(self, sheet):
        try:
            fields = {
                "样车/样件名称": self.config.SAMPLE_NAME,
                "样件所属部门": self.config.SAMPLE_DEPARTMENT,
                "状态要求\n（按需填写）": "S2",
                "数量": "1",
                "免费样件数量\n（如有）": "/",
                "需请购数量\n（数量减免费样件数量）": "/",
                "备注": "/"
            }

            for row in sheet.iter_rows():
                for cell in row:
                    if not cell.value:
                        continue
                    for key, value in fields.items():
                        if key == str(cell.value).strip():
                            target_cell = sheet.cell(row=cell.row + 1, column=cell.column)
                            target_cell.value = value
                            target_cell.alignment = Alignment(horizontal='center', vertical='center')
                            logger.info(f"更新样车样件字段: {key} -> {value} at {target_cell.coordinate}")
                            break

        except Exception as e:
            logger.error(f"更新样车样件需求 sheet 失败: {str(e)}")
            self.missed_fields.append(f"样车样件需求 sheet 更新错误: {str(e)}")

    def save_file(self, output_path: str):
        try:
            self.wb.save(output_path)
            logger.info(f"文件已保存至: {output_path}")
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")

    def process(self, output_path: str):
        if not self.load_workbook():
            return
        if not self.clean_sheets():
            return
        plan_sheet = self.find_plan_sheet()
        sample_sheet = self.find_sample_sheet()
        if not plan_sheet or not sample_sheet:
            return
        self.update_plan_sheet(plan_sheet)
        self.update_sample_sheet(sample_sheet)
        if self.missed_fields:
            logger.warning("以下字段未成功更新:")
            for field in self.missed_fields:
                logger.warning(field)
        self.save_file(output_path)