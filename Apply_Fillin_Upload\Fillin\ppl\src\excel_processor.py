import os
import re
import logging
from datetime import datetime
from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.cell.cell import MergedCell
import uuid
from cell_utils import find_cell, fill_cell, replace_project_vse

logger = logging.getLogger(__name__)


def validate_sheets(workbook, required_sheets: dict) -> bool:
    """Verify that the Excel file contains the required sheets.

    Args:
        workbook: openpyxl workbook object.
        required_sheets (dict): Patterns for required sheet names.

    Returns:
        bool: Whether validation passes.
    """
    # Only consider visible sheets
    visible_sheets = [sheet for sheet in workbook if sheet.sheet_state == 'visible']
    sheet_names = [sheet.title for sheet in visible_sheets]

    # Log all sheet information, including hidden status
    logger.info("Sheet information:")
    for sheet in workbook:
        logger.info(f"  - {sheet.title} (status: {sheet.sheet_state})")

    if len(visible_sheets) != 3:
        logger.warning(f"File {workbook} contains {len(visible_sheets)} visible sheets, expected 3")
        return False

    patterns = {
        'change_history': required_sheets['change_history'],
        'dev_plan': required_sheets['dev_plan'],
        'checklist': required_sheets['checklist']
    }
    matched = {key: False for key in patterns}

    for sheet_name in sheet_names:
        for key, pattern in patterns.items():
            if re.search(pattern, sheet_name, re.IGNORECASE):
                matched[key] = True
                logger.info(f"Matched {key}: {sheet_name}")

    for key, found in matched.items():
        if not found:
            logger.warning(f"Missing sheet for {key}, expected pattern: {patterns[key]}")
            return False

    logger.info("Sheet validation passed")
    return True


def find_target_files(input_dir: str, pattern: str) -> list:
    """Find Excel files in the input folder matching the pattern.

    Args:
        input_dir (str): Relative input folder path (e.g., 'inputs').
        pattern (str): Regular expression for file name matching.

    Returns:
        list: List of matching file paths (absolute).

    Raises:
        FileNotFoundError: Input folder does not exist.
    """
    try:
        # Compute absolute path to input folder (ppl/inputs/)
        abs_input_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", input_dir))
        if not os.path.exists(abs_input_dir):
            raise FileNotFoundError(f"Input folder {abs_input_dir} does not exist")

        files = []
        for file in os.listdir(abs_input_dir):
            if re.search(pattern, file, re.IGNORECASE) and file.endswith('.xlsx'):
                files.append(os.path.join(abs_input_dir, file))
        logger.info(f"Found {len(files)} matching files: {files}")
        return files
    except Exception as e:
        logger.error(f"Error finding files: {e}")
        raise


def process_file(file_path: str, output_dir: str, config: dict):
    """Process a single Excel file.

    Args:
        file_path (str): Input file path (absolute).
        output_dir (str): Output folder path (absolute).
        config (dict): Configuration data.
    """
    try:
        logger.info(f"Starting processing file: {file_path}")
        wb = load_workbook(file_path)

        if not validate_sheets(wb, config['sheets']):
            logger.error(f"File {file_path} sheet validation failed, skipping")
            return

        current_date = datetime.now().strftime("%Y.%m.%d")

        # Process only visible sheets
        visible_sheets = [sheet for sheet in wb if sheet.sheet_state == 'visible']
        for ws in visible_sheets:
            # Fill version
            row, col, _ = find_cell(ws, "版本")
            if row and col:
                fill_cell(ws, row, col, config['data']['version'])
            else:
                logger.warning(f"Version cell not found in sheet {ws.title}")

            # Replace project VSE software
            replace_project_vse(ws, config['data']['project_code'])

        # Process development plan sheet
        dev_plan_pattern = config['sheets']['dev_plan']
        dev_plan_sheet = None
        for sheet in visible_sheets:
            if re.search(dev_plan_pattern, sheet.title, re.IGNORECASE):
                dev_plan_sheet = sheet
                break

        if dev_plan_sheet:
            # Fill responsible person fields
            fields = [
                ("编制", config['data']['model_responsible']),
                ("审核", config['data']['app_manager']),
                ("批准", config['data']['division_manager']),
                ("项目主管", config['data']['project_leader']),
                ("相关系统及部门会签", config['data']['PPL_Signatory']),
                ("研发经理", config['data']['rd_manager'])
            ]

            for target_value, fill_value in fields:
                row, col, _ = find_cell(dev_plan_sheet, target_value)
                if row and col:
                    fill_cell(dev_plan_sheet, row, col, fill_value)
                else:
                    logger.warning(f"Field '{target_value}' not found in development plan sheet")

            # Fill date fields (filter for 宋体, non-bold)
            date_count = 0
            first_date_skipped = False
            for row in dev_plan_sheet.iter_rows():
                for cell in row:
                    if isinstance(cell, MergedCell):
                        continue
                    if cell.value and "日期" in str(cell.value).lower():
                        font = cell.font
                        if not first_date_skipped:
                            # Skip first date (微软雅黑, bold)
                            if font.name == "微软雅黑" and font.bold:
                                logger.info(
                                    f"Skipping first date cell ({cell.row}, {cell.column}), font: {font.name}, bold: {font.bold}")
                                first_date_skipped = True
                                continue
                        # Fill other dates (宋体, non-bold)
                        if font.name == "宋体" and not font.bold:
                            fill_cell(dev_plan_sheet, cell.row, cell.column, current_date)
                            date_count += 1
                        else:
                            logger.warning(
                                f"Date cell ({cell.row}, {cell.column}) has invalid font, font: {font.name}, bold: {font.bold}")
            if date_count != 5:
                logger.warning(f"Found {date_count} valid date fields in development plan sheet, expected 5")

        # Save file
        output_filename = os.path.basename(file_path)
        output_path = os.path.join(output_dir, output_filename)
        wb.save(output_path)
        logger.info(f"File processing complete, saved to: {output_path}")

    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}")
        raise