import yaml
import pandas as pd
import re
import logging
import os

logger = logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """加载配置文件和Excel数据。

    Args:
        config_path (str): 配置文件路径（config.yaml）。

    Returns:
        dict: 合并后的配置内容。

    Raises:
        FileNotFoundError: 配置文件或Excel文件不存在。
        yaml.YAMLError: 配置文件格式错误。
        pd.errors.EmptyDataError: Excel文件为空。
    """
    try:
        # 加载YAML配置文件（默认值）
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 使用__file__计算Fill_Template_Data.xlsx的绝对路径
        data_file = os.path.abspath(
            os.path.join(
                os.path.dirname(__file__),
                "..",
                "..",
                "..",
                "Fill_Template_Data.xlsx"
            )
        )

        # 加载Excel数据
        df = pd.read_excel(data_file, sheet_name='Info')
        if df.empty:
            raise pd.errors.EmptyDataError(f"Excel文件 {data_file} 为空")

        # 角色映射
        role_mapping = {
            'VehicleCode': 'project_code',
            'VehicleLead': 'model_responsible',
            'ApplicationManager': 'app_manager',
            'DivisionManager': 'division_manager',
            'ChassisProjectManager': 'project_leader',
            'PPL_Signatory': 'PPL_Signatory',
            'RD_Manager': 'rd_manager'
        }

        # 处理people列，统一分隔符为半角逗号
        config_data = config['data'].copy()
        for _, row in df.iterrows():
            role = row['role']
            people = str(row['people'])
            if role in role_mapping:
                # 处理多个人，可能包含全角逗号、半角逗号、空格或其他符号
                people = re.sub(r'[,\s，;]+', ',', people).strip(',')
                config_data[role_mapping[role]] = people
                logger.info(f"加载角色 {role}: {people}")

        config['data'] = config_data
        logger.info(f"从 {data_file} 加载配置数据: {config['data']}")
        return config

    except FileNotFoundError as e:
        logger.error(f"文件不存在: {e}")
        raise
    except yaml.YAMLError as e:
        logger.error(f"配置文件 {config_path} 格式错误: {e}")
        raise
    except pd.errors.EmptyDataError as e:
        logger.error(f"Excel文件 {data_file} 读取失败: {e}")
        raise
    except Exception as e:
        logger.error(f"加载配置时发生错误: {e}")
        raise