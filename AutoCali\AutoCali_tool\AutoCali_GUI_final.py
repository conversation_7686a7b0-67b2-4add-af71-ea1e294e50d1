import sys
import os
import subprocess
import platform
import shutil
import numpy as np
import pandas as pd
import scipy.io as scio
from tqdm import tqdm

from pathlib import Path
from datetime import datetime
import re
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGridLayout, QLabel, QPushButton, QTextEdit, QMessageBox,
    QProgressBar, QGroupBox, QFrame, QScrollArea, QDialog, QLineEdit,
    QDialogButtonBox, QFormLayout
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QProcess
from PyQt5.QtGui import QFont, QIcon

# 导入cantools和can库用于数据解析
try:
    from cantools.database import load_file
    from can import ASCReader, BLFReader

    CAN_TOOLS_AVAILABLE = True
except ImportError:
    CAN_TOOLS_AVAILABLE = False


class SSEParameterDialog(QDialog):
    """SSE标定参数输入对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("SSE标定参数输入")
        self.setModal(True)
        self.resize(300, 200)
        self.init_ui()

    def init_ui(self):
        layout = QFormLayout(self)

        # 创建输入框
        self.x1_input = QLineEdit()
        self.x1_input.setPlaceholderText("请输入x1的值 (如: 0.0)")
        self.y1_input = QLineEdit()
        self.y1_input.setPlaceholderText("请输入y1的值 (如: 0.0)")
        self.x_input = QLineEdit()
        self.x_input.setPlaceholderText("请输入x的值 (如: 0.0)")
        self.y_input = QLineEdit()
        self.y_input.setPlaceholderText("请输入y的值 (如: 0.0)")
j
        # 添加到布局
        layout.addRow("x1值:", self.x1_input)
        layout.addRow("y1值:", self.y1_input)
        layout.addRow("x值:", self.x_input)
        layout.addRow("y值:", self.y_input)

        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)

    def get_values(self):
        """获取输入的值"""
        try:
            x1 = float(self.x1_input.text() or "0.0")
            y1 = float(self.y1_input.text() or "0.0")
            x = float(self.x_input.text() or "0.0")
            y = float(self.y_input.text() or "0.0")
            return x1, y1, x, y
        except ValueError:
            return None


class AutoCaliWorker(QThread):
    """AutoCali工作线程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    console_output = pyqtSignal(str)

    def __init__(self, operation, autocali_instance, parameters=None):
        super().__init__()
        self.operation = operation
        self.autocali_instance = autocali_instance
        self.parameters = parameters

    def run(self):
        try:
            if self.operation == "parse_files":
                self.progress.emit("开始解析BLF/ASC文件...")
                result = self.autocali_instance.ext_from_log_file()
                if result:
                    self.finished.emit(True, "文件解析完成，请点击'运行MAT转换'按钮继续")
                else:
                    self.finished.emit(False, "文件解析失败")
            elif self.operation == "run_simulink":
                self.progress.emit("开始MAT转换...")
                result = self.autocali_instance.run_simulink()
                if result:
                    self.finished.emit(True, "MAT转换完成，请点击'转换为CSV'按钮继续")
                else:
                    self.finished.emit(False, "MAT转换失败")
            elif self.operation == "mat_to_csv":
                self.progress.emit("开始MAT转CSV...")
                result = self.autocali_instance.mat_to_csv()
                if result:
                    self.finished.emit(True, "CSV转换完成，请点击'编译软件'按钮继续")
                else:
                    self.finished.emit(False, "CSV转换失败")
            elif self.operation == "compile_software":
                self.progress.emit("开始编译软件...")
                result = self.autocali_instance.cali_coding()
                if result:
                    self.finished.emit(True, "cse软件编译完成，请点击'运行CSE标定'按钮继续")
                else:
                    self.finished.emit(False, "软件编译失败")
            elif self.operation == "cse_cali":
                self.progress.emit("开始CSE标定...")
                result = self.autocali_instance.cse_cali()
                if result:
                    self.finished.emit(True, "CSE标定完成，请点击'运行SSE标定'按钮完成流程")
                else:
                    self.finished.emit(False, "CSE标定失败")
            elif self.operation == "sse_cali":
                self.progress.emit("开始SSE标定...")
                result = self.autocali_instance.sse_cali(self.parameters)
                if result:
                    self.finished.emit(True, "SSE标定完成，所有流程已完成！")
                else:
                    self.finished.emit(False, "SSE标定失败")
        except Exception as e:
            self.finished.emit(False, f"操作失败: {str(e)}")


class AutoCaliCore:
    """核心功能逻辑，参考AutoCali.py"""

    def __init__(self):
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.blf_dbc_input = os.path.join(self.base_dir, "Input")
        self.mat_output = os.path.join(self.base_dir, "output_mat")
        self.csv_ip_dir = os.path.join(self.base_dir, "output_simmat")
        self.csv_op_dir = os.path.join(self.base_dir, "output_csv")
        self.cali_op_dir = os.path.join(self.base_dir, "output_cali")

        # P2S路径

        self.P2S_dir = os.path.join(os.path.dirname(self.base_dir), "simTrans", "P2S.py")
        self.python37_dir = os.path.join(os.path.join(
            os.path.join(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "simTrans"), ".venv"),
            "Scripts"), "python.exe")  # 使用Python3.7

        # SSE标定路径
        self.sse_cpp_dir = os.path.join(self.base_dir, "SSE_File", "CC_ASW", "VSE_ASW_Code")
        self.sse_coding_dir = os.path.join(self.base_dir, "SSE_File", "SUEB代码集成.sln")
        self.sse_cali_exe_dir = os.path.join(self.base_dir, "SSE_File", "x64", "Release", "SUEB代码集成.exe")

        # CSE标定路径
        self.cse_cpp_dir = os.path.join(self.base_dir, "SSE_File", "CC_ASW", "VSE_ASW_Code")
        self.cse_coding_dir = os.path.join(self.base_dir, "CSE_File", "CseCali.sln")
        self.cse_cali_exe_dir = os.path.join(self.base_dir, "CSE_File", "x64", "Release", "SUEB代码集成.exe")
        self.cse_execution = os.path.join(self.base_dir, "SSE_File", "CC_ASW", "VSE_APPInterface.cpp")

        # 输出回调函数
        self.output_callback = None

        # 确保所有目录存在
        self._ensure_directories()

    def set_output_callback(self, callback):
        """设置输出回调函数"""
        self.output_callback = callback

    def _print(self, message):
        """输出消息到控制台和GUI"""
        print(message)
        if self.output_callback:
            self.output_callback(message)

    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.blf_dbc_input, self.mat_output, self.csv_ip_dir,
            self.csv_op_dir, self.cali_op_dir
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    def ext_from_log_file(self):
        """解析BLF/ASC文件 - 使用原版函数"""
        try:
            if not CAN_TOOLS_AVAILABLE:
                self._print("缺少cantools库，请安装: pip install cantools python-can")
                return False

            self._print("开始解析BLF/ASC文件...")
            
            dbcListNotScale = [1]
            name_add_sig = True
            # 获取文件列表
            files_path = []
            files_name = []
            dbc_path = []
            for root, _, files in os.walk(self.blf_dbc_input):
                for file in files:
                    if file.endswith(".blf") | file.endswith(".asc") | file.endswith(".BLF") | file.endswith(".ASC"):
                        files_path.append(root)
                        files_name.append(file)

            for root, _, files in os.walk(self.blf_dbc_input):
                for file in files:
                    if file.endswith(".dbc"):
                        dbc_path.append(os.path.join(root, file))
            
            if not files_path:
                self._print("未找到BLF/ASC文件")
                return False
            
            if not dbc_path:
                self._print("未找到DBC文件")
                return False
            
            database_list = [load_file(item) for item in dbc_path]

            # 不进行解析信号列表，即比例因子为1、偏置为0
            sigListNotScale = [sig.name for i in dbcListNotScale for msgs in database_list[i].messages for sig in
                               msgs.signals]

            msg_ids_list = [[j.frame_id for j in i.messages] for i in database_list]

            file_idx = 0
            for item in tqdm([[path, name] for path, name in zip(files_path, files_name)]):
                [f_path, f_name] = item

                file_path = os.path.join(f_path, f_name)

                if ('.asc' in file_path) | ('.ASC' in file_path):
                    data = ASCReader(file_path)
                elif ('.blf' in file_path) | ('.BLF' in file_path):
                    data = BLFReader(file_path)
                else:
                    continue

                matdata = {}

                start_timestamp = 0.0
                flag = True  # blf含时间戳时需设置为True

                try:
                    for i, msg in enumerate(data):
                        msg_id = msg.arbitration_id
                        msg_data = msg.data
                        timestamp = msg.timestamp
                        if flag:
                            start_timestamp = timestamp
                            flag = False
                        timestamp = timestamp - start_timestamp

                        info = {}
                        info_no_scale = {}
                        for idx, msg_ids in enumerate(msg_ids_list):
                            if msg_id in msg_ids:
                                info = database_list[idx].decode_message(msg_id, msg_data, decode_choices=False,
                                                                         scaling=True,
                                                                         allow_truncated=True)
                                info_no_scale = database_list[idx].decode_message(msg_id, msg_data, decode_choices=False,
                                                                                   scaling=False, allow_truncated=True)
                        for key, value in info.items():
                            if key not in sigListNotScale:
                                if key in matdata:
                                    matdata[key].append([timestamp, value])
                                else:
                                    matdata[key] = [[timestamp, value]]
                            else:
                                if key in matdata:
                                    matdata[key].append([timestamp, info_no_scale[key]])
                                else:
                                    matdata[key] = [[timestamp, info_no_scale[key]]]
                except:
                    self._print(f_name + "解析过程有误！")

                # 信号名前缀加上sig_***
                rename_matdata = {}

                for key, value in matdata.items():
                    matdata[key] = np.array(matdata[key])
                    if name_add_sig:
                        rename_matdata['sig_' + key] = np.array(matdata[key])
                    else:
                        rename_matdata[key] = np.array(matdata[key])

                # 保存mat数据
                filepathw = os.path.join(self.mat_output, f_name[:-4] + ".mat")
                scio.savemat(filepathw, rename_matdata)
            
            self._print("BLF/ASC文件解析完成")
            return True
        
        except Exception as e:
            self._print(f"解析文件时出错: {str(e)}")
            return False

    def run_simulink(self):
        """运行MAT转换"""
        # subprocess.run([self.python37_dir, self.P2S_dir, self.mat_output])
        # print("mat转换结束")
        try:
            self._print("开始运行MAT转换...")

            # 检查P2S.py是否存在
            if not os.path.exists(self.P2S_dir):
                self._print(f"P2S.py文件不存在: {self.P2S_dir}")
                return False

            # 检查输入目录
            mat_files = [f for f in os.listdir(self.mat_output) if f.endswith('.mat')]
            if not mat_files:
                self._print("输出MAT目录中没有文件")
                return False

            self._print(f"找到 {len(mat_files)} 个MAT文件需要转换")

            result = subprocess.run([
                self.python37_dir, self.P2S_dir, self.mat_output
            ], capture_output=True, text=True, cwd=self.base_dir)

            self._print("MATLAB转换输出:")
            if result.stdout:
                self._print(result.stdout)
            if result.stderr:
                self._print("错误信息:")
                self._print(result.stderr)

            if result.returncode == 0:
                self._print("MAT转换完成")
                return True
            else:
                self._print(f"MAT转换失败，返回码: {result.returncode}")
                return False
        except Exception as e:
            self._print(f"运行MAT转换时出错: {str(e)}")
            return False

    def mat_to_csv(self):
        """MAT转CSV"""
        try:
            self._print("开始MAT转CSV...")

            signal_names = [
                'tout', 'sig_BCMPower_Gear_12D_S', 'sig_Gear_Position', 'sig_Gear_Status',
                'sig_EPB_Status', 'sig_ACU_LongitudeAcceleration_S', 'sig_ACU_LateralAcceleration_S',
                'sig_ACU_VerticalAcceleration_S', 'sig_ACU_Wx_RollRate_S', 'sig_ACU_Wy_PitchRate_S',
                'sig_ACU_YawRate_S', 'sig_ACU_LongitudeAcceleration_St_S', 'sig_ACU_LateralAcceleration_St_S',
                'sig_ACU_VerticalAcceleration_St_S', 'sig_ACU_Wx_RollRateSensor_St_S',
                'sig_ACU_Wy_PitchRateSensor_St_S', 'sig_ACU_YawRateSensor_St_S', 'sig_ABS_Active_122_S',
                'sig_TCS_Active_S', 'sig_VDC_Active_222_S', 'sig_ABS_Fault_122_S', 'sig_TCS_Fault_0F4_S',
                'sig_VDC_Fault_123_S', 'sig_WheelSpeed_FL_122_S', 'sig_WheelSpeed_FL_Status_122_S',
                'sig_WheelSpeed_FR_122_S', 'sig_WheelSpeed_FR_Status_122_S', 'sig_WheelSpeed_RL_122_S',
                'sig_WheelSpeed_RL_Status_122_S', 'sig_WheelSpeed_RR_122_S', 'sig_WheelSpeed_RR_Status_122_S',
                'sig_Actual_Throttle_Depth_S', 'sig_Actual_Throttle_Dep_Effect_S', 'sig_VCU_Brake_Depth_S',
                'sig_VCU_Brake_Depth_Virtual_Value_S', 'sig_Veh_drv_sty', 'sig_IPB_BRAKE_PEDAL_STATUS',
                'sig_IPB_Plunger_Pressure_321_S', 'sig_IPB_PlungerPressure_Status_321_S',
                'sig_Front_Torque_241_S', 'sig_Front_Torque_State_241_S', 'sig_Rear_Torque_251_S',
                'sig_Rear_Torque_State_251_S', 'sig_Torque_FL_0FC_S', 'sig_Torque_FR_0FC_S',
                'sig_Torque_RL_0FC_S', 'sig_Torque_RR_0FC_S', 'sig_Torque_State_FL_0FC_S',
                'sig_Torque_State_FR_0FC_S', 'sig_Torque_State_RL_0FC_S', 'sig_Torque_State_RR_0FC_S',
                'sig_Sensor_Calibration_Stats_11F_S', 'sig_Failure_Stats_OK_11F_S',
                'sig_Steering_Wheel_Angle_11F_S', 'sig_Steering_Wheel__Speed_11F_S', 'sig_AccelForward',
                'sig_AccelLateral', 'sig_AngleSlip'
            ]

            processed_files = 0
            for root, dirs, files in os.walk(self.csv_ip_dir):
                for file in files:
                    if file.endswith(".mat"):
                        self._print(f"转换文件: {file}")
                        self._convert_mat_to_csv(root, file, signal_names)
                        processed_files += 1

            self._print(f"MAT转CSV完成，处理了 {processed_files} 个文件")
            return processed_files > 0

        except Exception as e:
            self._print(f"MAT转CSV时出错: {str(e)}")
            return False

    def _convert_mat_to_csv(self, root, file, signal_names):
        """转换单个MAT文件为CSV"""
        try:
            mat_data = scipy.io.loadmat(os.path.join(root, file))

            data_dict = {}
            max_length = 0

            # 确定最大信号长度
            for name in signal_names:
                if name in mat_data:
                    signal = mat_data[name]
                    if signal.size > 0:
                        signal_flat = signal.flatten()
                        if len(signal_flat) > max_length:
                            max_length = len(signal_flat)

            if max_length == 0:
                max_length = 1

            # 提取数据
            for name in signal_names:
                if name in mat_data:
                    signal = mat_data[name]
                    if signal.size > 0:
                        signal_flat = signal.flatten()
                        if len(signal_flat) < max_length:
                            padded_signal = np.zeros(max_length)
                            padded_signal[:len(signal_flat)] = signal_flat
                            data_dict[name] = padded_signal
                        else:
                            data_dict[name] = signal_flat
                    else:
                        data_dict[name] = np.zeros(max_length)
                else:
                    data_dict[name] = np.zeros(max_length)

            # 保存CSV
            df = pd.DataFrame(data_dict)
            csv_name = file.replace(".mat", ".csv")
            csv_path = os.path.join(self.csv_op_dir, csv_name)
            df.to_csv(csv_path, index=False)
            self._print(f"转换完成: {csv_path}")
        except Exception as e:
            self._print(f"转换文件 {file} 失败: {str(e)}")

    def cali_coding(self):
        """编译软件"""
        try:
            self._print("开始编译软件...")

            # 转换.c文件为.cpp
            self._print("转换.c文件为.cpp...")
            converted_count = 0
            for root, dirs, files in os.walk(self.blf_dbc_input):
                for file_name in files:
                    if file_name.endswith('.c'):
                        old_path = os.path.join(root, file_name)
                        new_path = os.path.join(root, file_name[:-2] + '.cpp')
                        os.rename(old_path, new_path)
                        self._print(f"转换: {file_name} -> {file_name[:-2]}.cpp")
                        converted_count += 1

            if converted_count == 0:
                self._print("未找到需要转换的.c文件")

            # 复制代码文件
            self._print("复制代码文件...")
            copied_count = 0
            for root, dirs, files in os.walk(self.blf_dbc_input):
                for file_name in files:
                    if file_name.endswith(('.cpp', '.h')):
                        src_path = os.path.join(root, file_name)
                        # 确保目标目录存在
                        os.makedirs(self.cse_cpp_dir, exist_ok=True)
                        os.makedirs(self.sse_cpp_dir, exist_ok=True)

                        shutil.copy2(src_path, self.cse_cpp_dir)
                        shutil.copy2(src_path, self.sse_cpp_dir)
                        self._print(f"复制文件: {file_name}")
                        copied_count += 1

            if copied_count == 0:
                self._print("未找到需要复制的.cpp/.h文件")

            # 编译CSE
            self._print("编译CSE...")
            if os.path.exists(self.cse_coding_dir):
                cse_result = subprocess.run([
                    "msbuild.exe", self.cse_coding_dir, "/p:Configuration=Release"
                ], capture_output=True, text=True)

                self._print("CSE编译输出:")
                if cse_result.stdout:
                    self._print(cse_result.stdout)
                if cse_result.stderr:
                    self._print("CSE编译错误:")
                    self._print(cse_result.stderr)

                cse_success = cse_result.returncode == 0
            else:
                self._print(f"CSE项目文件不存在: {self.cse_coding_dir}")
                cse_success = False
            success = cse_success
            if success:
                self._print("cse软件编译完成")
            else:
                self._print("cse软件编译失败")
            return success
            # 编译SSE
            # self._print("编译SSE...")
            # if os.path.exists(self.sse_coding_dir):
            #     sse_result = subprocess.run([
            #         "msbuild.exe", self.sse_coding_dir, "/p:Configuration=Release"
            #     ], capture_output=True, text=True)
            #
            #     self._print("SSE编译输出:")
            #     if sse_result.stdout:
            #         self._print(sse_result.stdout)
            #     if sse_result.stderr:
            #         self._print("SSE编译错误:")
            #         self._print(sse_result.stderr)
            #
            #     sse_success = sse_result.returncode == 0
            # else:
            #     self._print(f"SSE项目文件不存在: {self.sse_coding_dir}")
            #     sse_success = False
            #
            # success = cse_success and sse_success
            # if success:
            #     self._print("软件编译完成")
            # else:
            #     self._print("软件编译失败")
            #


        except Exception as e:
            self._print(f"编译软件时出错: {str(e)}")
            return False

    def cse_cali(self):
        """CSE标定"""
        try:
            self._print("开始CSE标定...")
            
            csv_path = []
            csv_name = []
            for root, _, files in os.walk(self.csv_op_dir):
                for file in files:
                    if file.endswith("CRC.csv"):
                        name_split = file.split('.')
                        name_combine = os.path.join(self.cali_op_dir, name_split[0] + "output" + ".csv")
                        csv_path.append(os.path.join(root, file))
                        csv_name.append(name_combine)
            
            if not csv_path:
                self._print("未找到CRC.csv文件")
                return False
            
            self._print(f"找到 {len(csv_path)} 个CRC.csv文件")
            self._print(f"输入文件: {csv_path}")
            self._print(f"输出文件: {csv_name}")
            
            if not os.path.exists(self.cse_cali_exe_dir):
                self._print(f"CSE标定可执行文件不存在: {self.cse_cali_exe_dir}")
                return False
            
            i = 0
            while i < len(csv_path):
                self._print(f"处理文件 {i+1}/{len(csv_path)}: {os.path.basename(csv_path[i])}")
                opcse = subprocess.run([self.cse_cali_exe_dir, csv_path[i], csv_name[i]], 
                                     capture_output=True, text=True)
                output = opcse.stdout
                if output:
                    self._print(f"CSE输出 {i+1}: {output}")
                if opcse.stderr:
                    self._print(f"CSE错误 {i+1}: {opcse.stderr}")
                i += 1
            
            self._print("CSE标定完成")
            return True
        
        except Exception as e:
            self._print(f"CSE标定时出错: {str(e)}")
            return False

    def sse_cali(self, parameters=None):
        """SSE标定"""
        try:
            self._print("开始SSE标定...")

            if parameters:
                x1, y1, x, y = parameters
                self._print(f"使用参数: x1={x1}, y1={y1}, x={x}, y={y}")

                # 修改CPP文件中的参数
                if os.path.exists(self.cse_execution):
                    with open(self.cse_execution, 'r', encoding='utf-8') as f:
                        text = f.read()

                     # 植入标定量
                    text = re.sub(r'^\s*PosnX\s*=\s*[+-]?\d+(?:\.\d+)?\s*;',f'PosnX = {x:.2f};', text, flags=re.MULTILINE)
                    text = re.sub(r'^\s*PosnY\s*=\s*[+-]?\d+(?:\.\d+)?\s*;',f'PosnY = {y:.2f};', text, flags=re.MULTILINE)
                    text = re.sub(r'^\s*PosnX1\s*=\s*[+-]?\d+(?:\.\d+)?\s*;',f'PosnX1 = {x1:.2f};', text, flags=re.MULTILINE)
                    text = re.sub(r'^\s*PosnY1\s*=\s*[+-]?\d+(?:\.\d+)?\s*;',f'PosnY1 = {y1:.2f};', text, flags=re.MULTILINE)


                    with open(self.cse_execution, 'w', encoding='utf-8') as f:
                        f.write(text)


                    self._print("参数写入完成")
                else:
                    self._print(f"VSE_APPInterface.cpp文件不存在: {self.cse_execution}")

            self._print("编译SSE...")
            if os.path.exists(self.sse_coding_dir):
                sse_result = subprocess.run([
                    "msbuild.exe", self.sse_coding_dir, "/p:Configuration=Release"
                ], capture_output=True, text=True)

                self._print("SSE编译输出:")
                if sse_result.stdout:
                    self._print(sse_result.stdout)
                if sse_result.stderr:
                    self._print("SSE编译错误:")
                    self._print(sse_result.stderr)

                sse_success = sse_result.returncode == 0
            else:
                self._print(f"SSE项目文件不存在: {self.sse_coding_dir}")
                sse_success = False

            success = sse_success
            if success:
                self._print("sse软件编译完成")
            else:
                self._print("sse软件编译失败")


            # 获取CSV文件
            csv_path = []
            for root, _, files in os.walk(self.csv_op_dir):
                for file in files:
                    if file.endswith(".csv"):
                        csv_path.append(os.path.join(root, file))

            self._print(f"找到 {len(csv_path)} 个CSV文件进行SSE标定")

            if not os.path.exists(self.sse_cali_exe_dir):
                self._print(f"SSE标定可执行文件不存在: {self.sse_cali_exe_dir}")
                return False

            for i, input_path in enumerate(csv_path):
                self._print(f"处理文件 {i + 1}/{len(csv_path)}: {os.path.basename(input_path)}")
                result = subprocess.run([
                    self.sse_cali_exe_dir, input_path, self.cali_op_dir + "\\"
                ], capture_output=True, text=True)

                self._print(f"SSE标定输出 {i + 1}:")
                if result.stdout:
                    self._print(result.stdout)
                if result.stderr:
                    self._print(f"SSE标定错误 {i + 1}:")
                    self._print(result.stderr)

            self._print("SSE标定完成")
            return True

        except Exception as e:
            self._print(f"SSE标定时出错: {str(e)}")
            return False

    def open_folder(self, path):
        """打开文件夹"""
        if not os.path.exists(path):
            os.makedirs(path)
        if platform.system() == "Windows":
            os.startfile(path)
        else:
            QMessageBox.information(None, "提示", f"请手动打开: {path}")

    def clear_folders(self):
        """清空所有文件夹"""
        folders = [self.blf_dbc_input, self.mat_output, self.csv_ip_dir, self.csv_op_dir, self.cali_op_dir]
        for folder in folders:
            if os.path.exists(folder):
                shutil.rmtree(folder)
            os.makedirs(folder)


class AutoCaliGUI(QMainWindow):
    def __init__(self, on_home=None, on_back=None):
        super().__init__()
        self.on_home = on_home
        self.on_back = on_back
        self.core = AutoCaliCore()
        self.current_step = 0  # 当前步骤
        self.process_steps = [
            "解析BLF/ASC", "运行MAT转换", "转换为CSV",
            "编译软件", "运行CSE标定", "运行SSE标定"
        ]
        self.step_buttons = []  # 存储处理步骤按钮
        self.init_ui()
        self.log_message("欢迎使用自动标定工具！请先打开输入文件夹，放入BLF/ASC和DBC文件，然后依次点击处理按钮。")
        self.log_message(f"下一步：点击'{self.process_steps[0]}'按钮开始处理")

    def init_ui(self):
        self.setWindowTitle("自动标定工具")
        self.setGeometry(100, 100, 1200, 800)
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 顶部导航按钮
        nav_container = QWidget()
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 10)

        # 返回上一级按钮（橙色）
        back_btn = QPushButton("返回上一级")
        back_btn.setFont(QFont("Arial", 11))
        back_btn.setMaximumSize(120, 36)
        if self.on_back:
            back_btn.clicked.connect(self.on_back)
        else:
            back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        nav_layout.addWidget(back_btn)

        # 返回主页按钮（主色蓝色）
        home_btn = QPushButton("返回主页")
        home_btn.setFont(QFont("Arial", 11))
        home_btn.setMaximumSize(120, 36)
        if self.on_home:
            home_btn.clicked.connect(self.on_home)
        else:
            home_btn.clicked.connect(self.close)
        home_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        nav_layout.addWidget(home_btn)
        nav_layout.addStretch()
        main_layout.addWidget(nav_container)

        # 创建标题
        title_label = QLabel("自动标定工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0;")
        main_layout.addWidget(title_label)

        # 创建操作区域
        self.create_operation_area(main_layout)

        # 创建状态显示区域
        self.create_status_area(main_layout)

        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)

    def create_operation_area(self, main_layout):
        """创建操作区域"""
        # 创建文件夹操作组
        folder_group = QGroupBox("文件夹操作")
        folder_group.setFont(QFont("Arial", 11, QFont.Bold))
        folder_layout = QHBoxLayout(folder_group)

        # 文件夹操作按钮
        folder_buttons = [
            ("打开输入文件夹", self.open_input_folder, "#3498db"),
            ("打开输出文件夹", self.open_output_folder, "#27ae60"),
            ("清空文件夹", self.clear_folders, "#e74c3c")
        ]

        for text, handler, color in folder_buttons:
            btn = QPushButton(text)
            btn.setFont(QFont("Arial", 10))
            btn.setMinimumSize(120, 40)
            btn.clicked.connect(handler)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px;
                    font-size: 10pt;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            folder_layout.addWidget(btn)

        main_layout.addWidget(folder_group)

        # 创建数据处理操作组
        process_group = QGroupBox("数据处理流程（请按顺序执行）")
        process_group.setFont(QFont("Arial", 11, QFont.Bold))
        process_layout = QGridLayout(process_group)

        # 数据处理按钮
        process_buttons = [
            ("解析BLF/ASC", self.parse_files, "#9b59b6"),
            ("运行MAT转换", self.run_simulink, "#f39c12"),
            ("转换为CSV", self.mat_to_csv, "#1abc9c"),
            ("编译软件", self.compile_software, "#34495e"),
            ("运行CSE标定", self.cse_cali, "#e67e22"),
            ("运行SSE标定", self.sse_cali, "#d35400")
        ]

        self.step_buttons = []  # 清空之前的按钮列表
        for i, (text, handler, color) in enumerate(process_buttons):
            btn = QPushButton(text)
            btn.setFont(QFont("Arial", 11, QFont.Bold))
            btn.setMinimumSize(140, 50)
            btn.clicked.connect(handler)

            # 所有按钮都设为可用
            btn.setEnabled(True)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px;
                    font-size: 11pt;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, 0.7)};
                }}
            """)

            # 按2列布局排列
            row = i // 2
            col = i % 2
            process_layout.addWidget(btn, row, col)
            self.step_buttons.append(btn)

        main_layout.addWidget(process_group)

    def create_status_area(self, main_layout):
        """创建状态显示区域"""
        status_group = QGroupBox("状态信息")
        status_group.setFont(QFont("Arial", 11, QFont.Bold))
        status_layout = QVBoxLayout(status_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        # 状态文本
        self.status_text = QTextEdit()
        self.status_text.setMinimumHeight(200)
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 10))
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 10px;
                font-size: 10pt;
                font-family: 'Consolas', monospace;
            }
        """)
        status_layout.addWidget(self.status_text)

        main_layout.addWidget(status_group)

    def update_step_buttons(self):
        """更新步骤按钮状态 - 所有按钮都可用"""
        for i, button in enumerate(self.step_buttons):
            # 所有按钮都设为可用状态
            button.setEnabled(True)
            if i <= self.current_step:
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        border-radius: 6px;
                    }}
                    QPushButton:hover {{
                        background-color: #2ecc71;
                    }}
                """)
            else:
                # 保持原有样式但确保可用
                original_color = ["#9b59b6", "#f39c12", "#1abc9c", "#34495e", "#e67e22", "#d35400"][i]
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {original_color};
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        border-radius: 6px;
                    }}
                    QPushButton:hover {{
                        background-color: {original_color};
                        opacity: 0.8;
                    }}
                """)
    def get_button_color(self, step):
        """获取按钮颜色"""
        colors = ["#9b59b6", "#f39c12", "#1abc9c", "#34495e", "#e67e22", "#d35400"]
        return colors[step] if step < len(colors) else "#95a5a6"

    def darken_color(self, color, factor=0.85):
        """使颜色变暗"""
        color = color.lstrip('#')
        r, g, b = tuple(int(color[i:i + 2], 16) for i in (0, 2, 4))
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        return f"#{r:02x}{g:02x}{b:02x}"

    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        self.status_text.ensureCursorVisible()

    def show_progress(self, show=True):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_bar.setRange(0, 0)  # 无限进度条
        else:
            self.progress_bar.setRange(0, 1)
            self.progress_bar.setValue(1)

    def open_input_folder(self):
        """打开输入文件夹"""
        try:
            self.core.open_folder(self.core.blf_dbc_input)
            self.log_message(f"打开输入文件夹: {self.core.blf_dbc_input}")
        except Exception as e:
            self.log_message(f"打开输入文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开输入文件夹: {str(e)}")

    def open_output_folder(self):
        """打开输出文件夹"""
        try:
            self.core.open_folder(self.core.cali_op_dir)
            self.log_message(f"打开输出文件夹: {self.core.cali_op_dir}")
        except Exception as e:
            self.log_message(f"打开输出文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开输出文件夹: {str(e)}")

    def clear_folders(self):
        """清空文件夹"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有文件夹吗？",
                                     QMessageBox.Yes | QMessageBox.No,
                                     QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                self.core.clear_folders()
                self.log_message("所有文件夹已清空")
                # 重置流程
                self.current_step = 0
                self.update_step_buttons()
                self.log_message(f"下一步：点击'{self.process_steps[0]}'按钮开始处理")
            except Exception as e:
                self.log_message(f"清空文件夹失败: {str(e)}")
                QMessageBox.critical(self, "错误", f"清空文件夹失败: {str(e)}")

    def parse_files(self):
        """解析BLF/ASC文件"""
        self.show_progress(True)
        self.log_message("开始解析BLF/ASC文件...")
        # 设置输出回调
        self.core.set_output_callback(self.log_message)
        self.worker = AutoCaliWorker("parse_files", self.core)
        self.worker.progress.connect(self.log_message)
        self.worker.console_output.connect(self.log_message)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def run_simulink(self):
        """运行MAT转换"""
        self.show_progress(True)
        self.log_message("开始MAT转换...")
        self.core.set_output_callback(self.log_message)
        self.worker = AutoCaliWorker("run_simulink", self.core)
        self.worker.progress.connect(self.log_message)
        self.worker.console_output.connect(self.log_message)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def mat_to_csv(self):
        """MAT转CSV"""
        self.show_progress(True)
        self.log_message("开始MAT转CSV...")
        self.core.set_output_callback(self.log_message)
        self.worker = AutoCaliWorker("mat_to_csv", self.core)
        self.worker.progress.connect(self.log_message)
        self.worker.console_output.connect(self.log_message)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def compile_software(self):
        """编译软件"""
        self.show_progress(True)
        self.log_message("开始编译软件...")
        self.core.set_output_callback(self.log_message)
        self.worker = AutoCaliWorker("compile_software", self.core)
        self.worker.progress.connect(self.log_message)
        self.worker.console_output.connect(self.log_message)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def cse_cali(self):
        """CSE标定"""
        self.show_progress(True)
        self.log_message("开始CSE标定...")
        self.core.set_output_callback(self.log_message)
        self.worker = AutoCaliWorker("cse_cali", self.core)
        self.worker.progress.connect(self.log_message)
        self.worker.console_output.connect(self.log_message)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def sse_cali(self):
        """SSE标定"""
        # 弹出参数输入对话框
        dialog = SSEParameterDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            parameters = dialog.get_values()
            if parameters is None:
                QMessageBox.warning(self, "警告", "参数输入有误，请输入有效的数值")
                return

            self.show_progress(True)
            self.log_message(
                f"开始SSE标定，参数: x1={parameters[0]}, y1={parameters[1]}, x={parameters[2]}, y={parameters[3]}")
            self.core.set_output_callback(self.log_message)
            self.worker = AutoCaliWorker("sse_cali", self.core, parameters)
            self.worker.progress.connect(self.log_message)
            self.worker.console_output.connect(self.log_message)
            self.worker.finished.connect(self.on_operation_finished)
            self.worker.start()

    def on_operation_finished(self, success, message):
        """操作完成回调"""
        self.show_progress(False)
        self.log_message(message)

        if success:
            # 更新到下一步
            if self.current_step < len(self.process_steps) - 1:
                self.current_step += 1
                self.update_step_buttons()
                if self.current_step < len(self.process_steps):
                    self.log_message(f"下一步：点击'{self.process_steps[self.current_step]}'按钮继续")
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)


def main():
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    # 创建并显示窗口
    window = AutoCaliGUI()
    window.show()

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
