import os
import tkinter as tk
from tkinter import filedialog, messagebox
import shutil
import os
import can
from cantools.database import load_file
from can import ASCReader,BLFReader
import h5py
import numpy as np
import scipy.io as scio
from tqdm import tqdm
import subprocess
import pandas as pd
from decimal import Decimal
from asammdf import MDF
import importlib.util
import sys
import scipy.io
import pandas as pd
import platform


class FilePathUI:
    def __init__(self, root):
        self.root = root
        self.root.title("自动标定工具")

        # 设置窗口大小和位置
        window_width = 1200
        window_height = 200
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建UI元素
        self.create_widgets()

        """
        获取路径
        """

        # 文件路径
        self.main_dir = os.path.dirname(os.path.abspath(__file__))
        # Blf/DBC 输入路径
        self.blf_dbc_input = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Input")
        # self.target_dir_dbc = [os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)), "输入路径"), "VSE2.0_HT_20250530.dbc")]

        # 解析后.mat输出路径
        self.mat_output = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output_mat")

        # P2S 运行路径
        self.P2S_dir = os.path.join(
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "P2S"), "P2S.py")
        self.python37_dir = os.path.join(os.path.join(
            os.path.join(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "P2S"), ".venv"),
            "Scripts"), "python.exe")
        # self.P2S_mat_output = os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)), "input_dbc_blf"),"output_processed_mat")

        # .mat -> Csv 输入输出路径
        self.csv_ip_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output_simmat")
        self.csv_op_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output_csv")

        # SSE标定路径

        self.sse_coding_dir = os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)), "SSE_File"),
                                           "SUEB代码集成.sln")
        self.sse_cali_exe_dir = os.path.join(os.path.join(os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)), "SSE_File"), "x64"), "Release"),
                                             "SUEB代码集成.exe")
        self.cali_op_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output_cali")

        # CSE标定路径
        self.cse_coding_dir = os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)), "CSE_File"),
                                           "CseCali.sln")
        self.cse_cali_exe_dir = os.path.join(os.path.join(os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)), "CSE_File"), "x64"), "Release"),
                                             "SUEB代码集成.exe")



        # DM自动化路径

        # RTE自动化路径

        # Simulink仿真环境路径
    def create_widgets(self):
        # 说明标签
        self.label = tk.Label(self.root, text="请选择要放入的文件", font=("Arial", 12))
        self.label.pack(pady=15)

        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=5)

        # 选择文件按钮
        self.select_button = tk.Button(button_frame, text="打开输入文件夹", command=self.open_input_folder, height=2, width=15)
        self.select_button.pack(side=tk.LEFT, padx=10)

        # 选择文件按钮
        self.select_button = tk.Button(button_frame, text="打开输出文件夹", command=self.open_output_folder, height=2, width=15)
        self.select_button.pack(side=tk.LEFT, padx=10)

        # 清空文件夹按钮
        self.clear_button = tk.Button(button_frame, text="清空文件夹", command=self.clear_folder, height=2, width=15)
        self.clear_button.pack(side=tk.LEFT, padx=10)

        # 运行解析按钮
        self.clear_button = tk.Button(button_frame, text="解析blf/asc", command=self.ext_from_log_file, height=2, width=15)
        self.clear_button.pack(side=tk.LEFT, padx=10)

        # 运行工程脚本按钮
        self.run_button = tk.Button(button_frame, text="运行mat转换", command=self.run_simulink, height=2, width=15)
        self.run_button.pack(side=tk.LEFT, padx=5)

        # 运行mat转换csv按钮
        self.clear_button = tk.Button(button_frame, text="转换为csv", command=self.mat_to_csv, height=2, width=15)
        self.clear_button.pack(side=tk.LEFT, padx=10)

        # 运行CSE及SSE编译按钮
        self.run_button = tk.Button(button_frame, text="编译软件", command=self.cali_coding, height=2, width=15)
        self.run_button.pack(side=tk.LEFT, padx=5)

        # 运行CSE标定脚本按钮
        self.run_button = tk.Button(button_frame, text="运行CSE标定", command=self.cse_cali, height=2, width=15)
        self.run_button.pack(side=tk.LEFT, padx=5)

        # 运行SSE标定脚本按钮
        self.run_button = tk.Button(button_frame, text="运行SSE标定", command=self.sse_cali, height=2, width=15)
        self.run_button.pack(side=tk.LEFT, padx=5)

        # 状态标签
        self.status_label = tk.Label(self.root, text="", fg="blue")
        self.status_label.pack(pady=10)

    def select_file(self):
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(title="选择要放入的文件")

        if file_path:  # 如果用户选择了文件
            # 确保目标目录存在
            if not os.path.exists(self.target_dir):
                os.makedirs(self.target_dir)
                self.status_label.config(text=f"已创建目录: {self.target_dir}", fg="green")

            try:
                # 获取文件名
                file_name = os.path.basename(file_path)
                # 目标文件路径
                dest_path = os.path.join(self.target_dir, file_name)

                # 复制文件到目标目录
                with open(file_path, 'rb') as src_file:
                    with open(dest_path, 'wb') as dest_file:
                        dest_file.write(src_file.read())

                # 更新状态
                self.status_label.config(text=f"文件 {file_name} 已成功放入目录", fg="green")
                messagebox.showinfo("成功", f"文件 {file_name} 已成功放入目录:\n{self.target_dir}")

                # 列出目录中的文件
                files = os.listdir(self.target_dir)
                if files:
                    file_list = "\n".join(f"- {f}" for f in files)
                    messagebox.showinfo("目录内容", f"当前目录中的文件:\n{file_list}")

            except Exception as e:
                self.status_label.config(text=f"错误: {str(e)}", fg="red")
                messagebox.showerror("错误", f"无法复制文件: {str(e)}")

    def open_input_folder(self):
        """打开指定文件夹（支持Windows/macOS/Linux）"""
        # 检查路径是否存在
        if not os.path.exists(self.blf_dbc_input):
            raise FileNotFoundError(f"路径不存在: {self.blf_dbc_input}")

        # 标准化路径格式
        normalized_path = os.path.normpath(self.blf_dbc_input)

        # 根据操作系统使用不同的打开方式
        system = platform.system()
        os.startfile(normalized_path)
    def open_output_folder(self):
        """打开指定文件夹（支持Windows/macOS/Linux）"""
        # 检查路径是否存在
        if not os.path.exists(self.cali_op_dir):
            raise FileNotFoundError(f"路径不存在: {self.cali_op_dir}")

        # 标准化路径格式
        normalized_path = os.path.normpath(self.cali_op_dir)

        # 根据操作系统使用不同的打开方式
        system = platform.system()
        os.startfile(normalized_path)

    def open_output_folder2(self):
        """打开指定文件夹（支持Windows/macOS/Linux）"""
        # 检查路径是否存在
        if not os.path.exists(self.target_dir_output):
            raise FileNotFoundError(f"路径不存在: {self.target_dir_output}")

        # 标准化路径格式
        normalized_path = os.path.normpath(self.target_dir_output)

        # 根据操作系统使用不同的打开方式
        system = platform.system()
        os.startfile(normalized_path)

    def open_output_folder3(self):
        """打开指定文件夹（支持Windows/macOS/Linux）"""
        # 检查路径是否存在
        if not os.path.exists(self.target_dir_output):
            raise FileNotFoundError(f"路径不存在: {self.target_dir_output}")

        # 标准化路径格式
        normalized_path = os.path.normpath(self.target_dir_output)

        # 根据操作系统使用不同的打开方式
        system = platform.system()
        os.startfile(normalized_path)

    def select_outputfile(self):
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(title="选择要放入的文件")

        if file_path:  # 如果用户选择了文件
            # 确保目标目录存在
            if not os.path.exists(self.target_dir_output):
                os.makedirs(self.target_dir_output)
                self.status_label.config(text=f"已创建目录: {self.target_dir_output}", fg="green")

            try:
                # 获取文件名
                file_name = os.path.basename(file_path)
                # 目标文件路径
                dest_path = os.path.join(self.target_dir_output, file_name)

                # 复制文件到目标目录
                with open(file_path, 'rb') as src_file:
                    with open(dest_path, 'wb') as dest_file:
                        dest_file.write(src_file.read())

                # 更新状态
                self.status_label.config(text=f"文件 {file_name} 已成功放入目录", fg="green")
                messagebox.showinfo("成功", f"文件 {file_name} 已成功放入目录:\n{self.target_dir}")

                # 列出目录中的文件
                files = os.listdir(self.target_dir)
                if files:
                    file_list = "\n".join(f"- {f}" for f in files)
                    messagebox.showinfo("目录内容", f"当前目录中的文件:\n{file_list}")

            except Exception as e:
                self.status_label.config(text=f"错误: {str(e)}", fg="red")
                messagebox.showerror("错误", f"无法复制文件: {str(e)}")

    def clear_folder(self):
        #所有输入输出文件夹
        Outputfile_list = [self.blf_dbc_input, self.csv_ip_dir, self.csv_op_dir, self.cali_op_dir, self.mat_output]

        # 确认用户是否真的要清空文件夹
        confirm = messagebox.askyesno("确认", "确定清空？")
        i = 0
        while i < len(Outputfile_list):
            if confirm:
                try:
                    # 检查文件夹是否存在
                    if os.path.exists(Outputfile_list[i]):
                        # 删除文件夹及其内容
                        shutil.rmtree(Outputfile_list[i])
                        # 重新创建空文件夹
                        os.makedirs(Outputfile_list[i])
                        self.status_label.config(text="文件夹已清空", fg="green")

                    else:
                        self.status_label.config(text="用户文件夹不存在", fg="blue")
                        messagebox.showinfo("信息", "用户文件夹不存在")
                except Exception as e:
                    self.status_label.config(text=f"清空文件夹错误: {str(e)}", fg="red")
                    messagebox.showerror("错误", f"无法清空文件夹: {str(e)}")
            i += 1
        messagebox.showinfo("成功", "所有文件夹已成功清空")

    def ext_from_log_file(self):
        '''
        数据解析
        :param fileDir: 原始数据存放路径
        :param mat_fileDir: 解析后.mat数据存放路径
        :param dbc_fileDir: dbc文件路径
        :param dbcListNotScale: dbc不解析只转发值，即比例系数为1、偏置为0.下标从零开始
        :param name_add_sig: 信号名前缀加上sig_***
        :return:
        '''
        dbcListNotScale = [1]
        name_add_sig = True
        # 获取文件列表
        files_path = []
        files_name = []
        dbc_path = []
        for root, _, files in os.walk(self.blf_dbc_input):
            for file in files:
                if file.endswith(".blf") | file.endswith(".asc") | file.endswith(".BLF") | file.endswith(".ASC"):
                    files_path.append(root)
                    files_name.append(file)

        for root, _, files in os.walk(self.blf_dbc_input):
            for file in files:
                if file.endswith(".dbc") :
                    dbc_path.append(os.path.join(root, file))
        database_list = [load_file(item) for item in dbc_path]

        # 不进行解析信号列表，即比例因子为1、偏置为0
        sigListNotScale = [sig.name for i in dbcListNotScale for msgs in database_list[i].messages for sig in
                           msgs.signals]

        msg_ids_list = [[j.frame_id for j in i.messages] for i in database_list]

        file_idx = 0
        for item in tqdm([[path, name] for path, name in zip(files_path, files_name)]):
            [f_path, f_name] = item

            file_path = os.path.join(f_path, f_name)

            if ('.asc' in file_path) | ('.ASC' in file_path):
                data = ASCReader(file_path)
            elif ('.blf' in file_path) | ('.BLF' in file_path):
                data = BLFReader(file_path)
            else:
                continue

            matdata = {}

            start_timestamp = 0.0
            flag = True  # blf含时间戳时需设置为True

            if ('.asc' in file_path) | ('.ASC' in file_path):
                data = ASCReader(file_path)
            elif ('.blf' in file_path) | ('.BLF' in file_path):
                data = BLFReader(file_path)
            else:
                continue

            try:
                for i, msg in enumerate(data):
                    # if msg.channel == 1:
                    #     continue
                    msg_id = msg.arbitration_id
                    msg_data = msg.data
                    timestamp = msg.timestamp
                    if flag:
                        start_timestamp = timestamp
                        flag = False
                    timestamp = timestamp - start_timestamp

                    info = {}
                    info_no_scale = {}
                    for idx, msg_ids in enumerate(msg_ids_list):
                        if msg_id in msg_ids:
                            info = database_list[idx].decode_message(msg_id, msg_data, decode_choices=False,
                                                                     scaling=True,
                                                                     allow_truncated=True)
                            info_no_scale = database_list[idx].decode_message(msg_id, msg_data, decode_choices=False,
                                                                              scaling=False, allow_truncated=True)
                    for key, value in info.items():
                        if key not in sigListNotScale:
                            if key in matdata:
                                matdata[key].append([timestamp, value])
                            else:
                                matdata[key] = [[timestamp, value]]
                        else:
                            if key in matdata:
                                matdata[key].append([timestamp, info_no_scale[key]])
                            else:
                                matdata[key] = [[timestamp, info_no_scale[key]]]
            except:
                print(f_name + "解析过程有误！")

            # 信号名前缀加上sig_***
            rename_matdata = {}

            for key, value in matdata.items():
                matdata[key] = np.array(matdata[key])
                if name_add_sig:
                    rename_matdata['sig_' + key] = np.array(matdata[key])
                else:
                    rename_matdata[key] = np.array(matdata[key])

            # 保存mat数据
            filepathw = os.path.join(self.mat_output, f_name[:-4] + ".mat")
            scio.savemat(filepathw, rename_matdata)

    def run_simulink(self):
        subprocess.run([self.python37_dir, self.P2S_dir, self.mat_output])




    def cali_coding(self):
        subprocess.run([f"msbuild.exe", self.cse_coding_dir, f"/p:Configuration=Release"])
        print("CSE完成编译")
        subprocess.run([f"msbuild.exe", self.sse_coding_dir, f"/p:Configuration=Release"])
        print("SSE完成编译")

    def cse_cali(self):
        csv_path =[]
        csv_name = []
        for root, _, files in os.walk(self.csv_op_dir):
            for file in files:
                if file.endswith("CRC.csv"):
                    name_split = file.split('.')
                    name_combine = os.path.join(self.cali_op_dir,name_split[0] + "output" + ".csv")
                    csv_path.append(os.path.join(root, file))
                    csv_name.append(name_combine)
        print(csv_path)
        print(csv_name)
        i = 0
        while i < len(csv_path):
            subprocess.run([self.cse_cali_exe_dir, csv_path[i], csv_name[i]])
            i += 1
        i = 0
        print("CSE完成标定")

    def sse_cali(self):
        csv_path =[]
        csv_name = []
        for root, _, files in os.walk(self.csv_op_dir):
            for file in files:
                if file.endswith(".csv"):
                    name_split = file.split('.')
                    name_combine = os.path.join(self.cali_op_dir, name_split[0] + "output" + ".csv")
                    csv_path.append(os.path.join(root, file))
                    csv_name.append(name_combine)
        print(csv_path)
        print(csv_name)
        print(self.cali_op_dir)
        i = 0
        while i < len(csv_path):
            subprocess.run([self.sse_cali_exe_dir, csv_path[i], self.cali_op_dir + "\\"])
            i += 1
        i = 0
        print("SSE完成标定")
        # subprocess.run([self.sse_cali_exe_dir, self.sse_cali_ip_dir])

    def mat_to_csv(self):
        """
        将.mat文件中的指定信号转换为CSV格式
        """
        signal_names = ['tout', 'sig_BCMPower_Gear_12D_S', 'sig_Gear_Position', 'sig_Gear_Status', 'sig_EPB_Status',
                        'sig_ACU_LongitudeAcceleration_S', 'sig_ACU_LateralAcceleration_S',
                        'sig_ACU_VerticalAcceleration_S', 'sig_ACU_Wx_RollRate_S', 'sig_ACU_Wy_PitchRate_S',
                        'sig_ACU_YawRate_S', 'sig_ACU_LongitudeAcceleration_St_S', 'sig_ACU_LateralAcceleration_St_S',
                        'sig_ACU_VerticalAcceleration_St_S', 'sig_ACU_Wx_RollRateSensor_St_S',
                        'sig_ACU_Wy_PitchRateSensor_St_S', 'sig_ACU_YawRateSensor_St_S', 'sig_ABS_Active_122_S',
                        'sig_TCS_Active_S', 'sig_VDC_Active_222_S', 'sig_ABS_Fault_122_S', 'sig_TCS_Fault_0F4_S',
                        'sig_VDC_Fault_123_S', 'sig_WheelSpeed_FL_122_S', 'sig_WheelSpeed_FL_Status_122_S',
                        'sig_WheelSpeed_FR_122_S', 'sig_WheelSpeed_FR_Status_122_S', 'sig_WheelSpeed_RL_122_S',
                        'sig_WheelSpeed_RL_Status_122_S', 'sig_WheelSpeed_RR_122_S', 'sig_WheelSpeed_RR_Status_122_S',
                        'sig_Actual_Throttle_Depth_S', 'sig_Actual_Throttle_Dep_Effect_S', 'sig_VCU_Brake_Depth_S',
                        'sig_VCU_Brake_Depth_Virtual_Value_S', 'sig_Veh_drv_sty', 'sig_IPB_BRAKE_PEDAL_STATUS',
                        'sig_IPB_Plunger_Pressure_321_S', 'sig_IPB_PlungerPressure_Status_321_S',
                        'sig_Front_Torque_241_S', 'sig_Front_Torque_State_241_S', 'sig_Rear_Torque_251_S',
                        'sig_Rear_Torque_State_251_S', 'sig_Torque_FL_0FC_S', 'sig_Torque_FR_0FC_S',
                        'sig_Torque_RL_0FC_S', 'sig_Torque_RR_0FC_S', 'sig_Torque_State_FL_0FC_S',
                        'sig_Torque_State_FR_0FC_S', 'sig_Torque_State_RL_0FC_S', 'sig_Torque_State_RR_0FC_S',
                        'sig_Sensor_Calibration_Stats_11F_S', 'sig_Failure_Stats_OK_11F_S',
                        'sig_Steering_Wheel_Angle_11F_S', 'sig_Steering_Wheel__Speed_11F_S', 'sig_AccelForward',
                        'sig_AccelLateral', 'sig_AngleSlip']
        if not os.path.exists(self.csv_ip_dir):
            os.mkdir(self.csv_ip_dir)
        for root, dir, files in os.walk(self.csv_ip_dir):
            for file in files:
                if file.endswith(".mat"):
                    # 加载.mat文件
                    mat_data = scipy.io.loadmat(os.path.join(root, file))

                    # 准备存储数据的字典
                    data_dict = {}
                    max_length = 0

                    # 第一轮遍历：确定最大信号长度
                    for name in signal_names:
                        if name in mat_data:
                            signal = mat_data[name]
                            # 跳过空值信号（大小为0）
                            if signal.size > 0:
                                # 展平多维数组为一维
                                signal_flat = signal.flatten()
                                if len(signal_flat) > max_length:
                                    max_length = len(signal_flat)

                    # 如果所有信号都为空，使用默认长度1
                    if max_length == 0:
                        max_length = 1

                    # 第二轮遍历：提取数据并处理空值
                    for name in signal_names:
                        if name in mat_data:
                            signal = mat_data[name]
                            if signal.size > 0:
                                signal_flat = signal.flatten()
                                # 如果信号长度不足，填充0
                                if len(signal_flat) < max_length:
                                    padded_signal = np.zeros(max_length)
                                    padded_signal[:len(signal_flat)] = signal_flat
                                    data_dict[name] = padded_signal
                                else:
                                    data_dict[name] = signal_flat
                            else:
                                # 空值信号填充0
                                data_dict[name] = np.zeros(max_length)
                        else:
                            # 不存在的信号填充0
                            data_dict[name] = np.zeros(max_length)

                    # 创建DataFrame并保存为CSV
                    df = pd.DataFrame(data_dict)
                    tmp_str = str.split(file, ".mat")[0]
                    df.to_csv(os.path.join(self.csv_op_dir, tmp_str + ".csv"), index=False)
                    print(f"成功转换 {len(signal_names)} 个信号到 {self.csv_op_dir}")






if __name__ == "__main__":
    root = tk.Tk()
    app = FilePathUI(root)
    root.mainloop()