import openpyxl
from datetime import datetime
import os
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Config:
    """从 Excel 文件动态加载配置，并提供默认值"""

    # 默认值
    VERSION = "001"
    CURRENT_DATE = datetime.now().strftime("%Y.%m.%d")
    DEPARTMENT = "CC架构部"
    SAMPLE_DEPARTMENT = "底盘电子控制应用部"

    # Excel 文件路径（基于 config.py 的位置，上两级目录）
    EXCEL_PATH = os.path.abspath(
        os.path.join(
            os.path.dirname(__file__),  # dvp/src/
            "..",  # dvp/
            "..",
            "..",  # Apply_Fillin_Upload/
            "Fill_Template_Data.xlsx"
        )
    )

    # 角色映射
    ROLE_MAPPING = {
        "VEHICLE_CODE": ["VehicleCode"],
        "SOFTWARE_ENGINEER": ["SectionHead"],
        "CORE_TEAM": ["Experts"],
        "COMPILER": ["VehicleLead"],
        "REVIEWER": ["FuncLead", "TeamLeader", "SectionHead", "Experts"],
        "AUDITOR": ["ApplicationManager"],
        "COUNTERSIGNER": ["ChassisProjectManager"],
        "QUALITY_MANAGER": ["RD_QualityManager"],
        "APPROVER": ["RD_Manager", "DivisionManager"]
    }

    def __init__(self):
        # 初始化默认值
        self.VEHICLE_CODE = ""
        self.SOFTWARE_ENGINEER = ""
        self.CORE_TEAM = ""
        self.COMPILER = ""
        self.REVIEWER = ""
        self.AUDITOR = ""
        self.COUNTERSIGNER = ""
        self.QUALITY_MANAGER = ""
        self.APPROVER = ""
        self.SAMPLE_NAME = ""

        # 加载 Excel 数据
        self._load_config()

    def _load_config(self):
        """从 Excel 文件加载配置"""
        try:
            logger.info(f"尝试加载 Excel 文件: {self.EXCEL_PATH}")
            if not os.path.exists(self.EXCEL_PATH):
                raise FileNotFoundError(f"未找到 Excel 文件: {self.EXCEL_PATH}")

            wb = openpyxl.load_workbook(self.EXCEL_PATH)
            sheet = wb["Info"]

            # 读取 Excel 数据
            role_to_people = {}
            for row in sheet.iter_rows(min_row=2, values_only=True):
                role, people = row[1], row[2]  # role 列和 people 列
                if role and people:
                    role_to_people[role] = str(people)  # 确保转换为字符串
                    logger.info(f"读取角色 {role}: {people}")

            # 填充配置
            for config_key, roles in self.ROLE_MAPPING.items():
                values = []
                for role in roles:
                    if role in role_to_people:
                        # 使用正则表达式分割多种分隔符
                        people_str = role_to_people[role]
                        # 匹配全角逗号、半角逗号、空格、分号、斜杠等
                        people_list = re.split(r'[,，;\s/]+', people_str)
                        # 去除空值和多余空格
                        people_list = [p.strip() for p in people_list if p.strip()]
                        values.extend(people_list)
                        logger.info(f"解析角色 {role} 的 people: {people_list}")

                # 合并值为字符串，唯一化以避免重复
                values = list(dict.fromkeys(values))  # 去重
                value = ", ".join(values) if values else ""
                setattr(self, config_key, value)
                logger.info(f"设置 {config_key}: {value}")

            # 特殊处理 SAMPLE_NAME
            if self.VEHICLE_CODE:
                self.SAMPLE_NAME = f"{self.VEHICLE_CODE}试制车"
                logger.info(f"设置 SAMPLE_NAME: {self.SAMPLE_NAME}")
            else:
                self.SAMPLE_NAME = "试制车"
                logger.info("VEHICLE_CODE 为空，使用默认 SAMPLE_NAME: 试制车")

        except Exception as e:
            logger.error(f"加载 Excel 配置失败: {str(e)}")
            # 使用默认值（空字符串或程序默认值）
            for config_key in self.ROLE_MAPPING:
                setattr(self, config_key, "")
            self.SAMPLE_NAME = "试制车"
            logger.info("已使用默认空值配置")