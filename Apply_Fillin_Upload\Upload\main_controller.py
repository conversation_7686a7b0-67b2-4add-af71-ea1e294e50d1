"""
主控制器模块
协调各个组件，执行完整的上传审批流程
"""

import os
import time
import logging
from pathlib import Path
from typing import List, Dict, Optional

from browser_manager import BrowserManager
from first_page_handler import FirstPageHandler
from second_page_handler import SecondPageHandler
from utils import FileProcessor, DataManager, ConfigManager, setup_logging, validate_config

class MainController:
    """主控制器"""
    
    def __init__(self):
        # 设置日志
        self.logger = setup_logging()
        
        # 加载配置
        self.config = ConfigManager.load_config()
        
        # 验证配置
        if not validate_config(self.config):
            raise ValueError("配置验证失败，请检查config.py文件")
        
        # 初始化组件
        self.browser_manager = BrowserManager(self.config)
        self.file_processor = FileProcessor()
        self.data_manager = DataManager(Path("Data"))
        
        # 页面处理器 - 在浏览器启动后初始化
        self.first_page_handler = None
        self.second_page_handler = None
        
        # 文件夹路径
        self.documents_folder = Path("Final_Approval_Documents")
        
        # 文件类型配置
        self.file_types = {
            "DVP": {
                "stage": "B版",
                "level": "项目级",
                "reviewer_scheme": "DVP评审方案"
            },
            "PPL": {
                "stage": "B版",
                "level": "项目级", 
                "reviewer_scheme": "PPL评审方案"
            },
            "FN": {
                "stage": "B版",
                "level": "项目级",
                "reviewer_scheme": "FN评审方案"
            }
        }
    
    def initialize_browser(self):
        """初始化浏览器"""
        try:
            self.logger.info("🚀 初始化浏览器...")
            
            # 启动浏览器
            self.browser_manager.setup_browser()
            
            # 初始化页面处理器
            self.first_page_handler = FirstPageHandler(
                self.browser_manager.driver,
                self.browser_manager.wait,
                self.config
            )
            self.second_page_handler = SecondPageHandler(
                self.browser_manager.driver,
                self.browser_manager.wait,
                self.config
            )
            
            # 登录
            self.browser_manager.login(
                self.config['USERNAME'],
                self.config['PASSWORD'],
                self.config['DMS_URL']
            )
            
            # 关闭弹窗
            self.browser_manager.close_popups()
            
            self.logger.info("✅ 浏览器初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 浏览器初始化失败: {str(e)}")
            raise
    
    def scan_documents(self) -> List[tuple]:
        """扫描待处理文档"""
        try:
            self.logger.info("📂 扫描待处理文档...")
            
            # 扫描文件
            files_by_type = self.file_processor.scan_files_for_processing(self.documents_folder)
            
            # 获取文档对
            document_pairs = self.file_processor.get_document_pairs(files_by_type)
            
            if not document_pairs:
                self.logger.warning("❌ 未找到待处理文档")
                return []
            
            self.logger.info(f"✅ 找到 {len(document_pairs)} 个文档待处理")
            return document_pairs
            
        except Exception as e:
            self.logger.error(f"❌ 文档扫描失败: {str(e)}")
            raise
    
    def process_single_document(self, main_file: Dict, excel_file: Optional[Dict] = None) -> bool:
        """处理单个文档"""
        try:
            doc_id = main_file['doc_id']
            file_type = main_file['type']
            
            self.logger.info(f"📄 开始处理文档: {doc_id} ({file_type})")
            
            # 导航到文档创建页面
            self.browser_manager.navigate_to_document_creation()
            
            # 第一页处理
            success = self.first_page_handler.fill_document_info(
                doc_id=doc_id,
                source_file=main_file,
                file_type=file_type,
                excel_file=excel_file
            )
            
            if not success:
                self.logger.error(f"❌ 第一页处理失败: {doc_id}")
                return False
            
            # 第二页处理 - 传递文件名用于FN文件类型识别
            filename = main_file.get('name', '') if main_file.get('name') else ''
            success = self.second_page_handler.handle_second_page(file_type, filename)
            
            if not success:
                self.logger.error(f"❌ 第二页处理失败: {doc_id}")
                return False
            
            # 提交完成后等待5秒
            time.sleep(5)
            
            self.logger.info(f"✅ 文档处理完成: {doc_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 处理文档失败 {main_file.get('doc_id', 'Unknown')}: {str(e)}")
            return False
    
    def prepare_for_next_document(self):
        """为下一个文档做准备 - 返回初始页面并重新登录"""
        try:
            self.logger.info("🔄 准备处理下一个文档...")
            
            # 关闭新窗口并回到主页面
            if not self.browser_manager.return_to_main_page():
                return False
            
            # 重新登录（无需账号密码，只需点击登录）
            if not self.browser_manager.quick_login():
                return False
            
            # 等待页面稳定
            time.sleep(3)
            
            self.logger.info("✅ 准备下一个文档完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 准备下一个文档失败: {str(e)}")
            return False
    
    def process_all_documents(self) -> Dict[str, int]:
        """处理所有文档"""
        try:
            # 扫描文档
            document_pairs = self.scan_documents()
            
            if not document_pairs:
                return {"total": 0, "success": 0, "failed": 0}
            
            # 统计结果
            results = {"total": len(document_pairs), "success": 0, "failed": 0}
            
            # 处理每个文档
            for i, (main_file, excel_file) in enumerate(document_pairs, 1):
                self.logger.info(f"🔄 处理进度: {i}/{len(document_pairs)}")
                
                try:
                    if self.process_single_document(main_file, excel_file):
                        results["success"] += 1
                        
                        # 如果还有更多文档需要处理，准备下一个文档
                        if i < len(document_pairs):
                            self.logger.info(f"📋 准备处理下一个文档 ({i+1}/{len(document_pairs)})...")
                            if not self.prepare_for_next_document():
                                self.logger.error("❌ 准备下一个文档失败，停止处理")
                                break
                    else:
                        results["failed"] += 1
                        
                        # 即使失败也要准备下一个文档
                        if i < len(document_pairs):
                            self.logger.info("⚠️ 文档处理失败，但继续准备下一个文档...")
                            if not self.prepare_for_next_document():
                                self.logger.error("❌ 准备下一个文档失败，停止处理")
                                break
                        
                except Exception as e:
                    self.logger.error(f"❌ 文档处理异常: {str(e)}")
                    results["failed"] += 1
                    
                    # 异常情况下也要尝试准备下一个文档
                    if i < len(document_pairs):
                        try:
                            self.prepare_for_next_document()
                        except Exception:
                            self.logger.error("❌ 异常恢复失败，停止处理")
                            break
            
            # 输出结果
            self.logger.info("📊 处理结果统计:")
            self.logger.info(f"  总计: {results['total']} 个文档")
            self.logger.info(f"  成功: {results['success']} 个文档")
            self.logger.info(f"  失败: {results['failed']} 个文档")
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 批量处理失败: {str(e)}")
            raise
    
    def run(self):
        """运行主流程"""
        try:
            self.logger.info("🎯 开始上传审批自动化流程")
            
            # 检查必要文件夹
            if not self.documents_folder.exists():
                self.logger.error(f"❌ 文档文件夹不存在: {self.documents_folder}")
                return False
            
            # 初始化浏览器
            self.initialize_browser()
            
            # 处理所有文档
            results = self.process_all_documents()
            
            # 判断执行结果
            if results["success"] > 0:
                self.logger.info("🎉 流程执行完成")
                return True
            else:
                self.logger.warning("⚠️ 流程执行完成，但没有成功处理任何文档")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 主流程执行失败: {str(e)}")
            return False
        finally:
            # 清理资源
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.browser_manager:
                self.browser_manager.quit()
            self.logger.info("🧹 资源清理完成")
        except Exception as e:
            self.logger.error(f"❌ 资源清理失败: {str(e)}")
    
    def force_cleanup(self):
        """强制清理资源，确保程序完全退出"""
        try:
            if self.browser_manager and self.browser_manager.driver:
                # 强制关闭浏览器
                self.browser_manager.driver.quit()
                self.logger.info("🔒 浏览器已强制关闭")
        except Exception as e:
            self.logger.error(f"❌ 强制清理失败: {str(e)}")
        
        # 清理其他资源
        self.browser_manager = None
        self.logger.info("🧹 所有资源已清理")


def main():
    """主入口函数"""
    controller = MainController()
    
    try:
        success = controller.run()
        if success:
            print("✅ 上传审批自动化流程执行完成")
        else:
            print("❌ 上传审批自动化流程执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        controller.force_cleanup()
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        controller.force_cleanup()
    finally:
        # 确保资源被完全清理
        controller.force_cleanup()
        print("🔚 程序已完全退出")


if __name__ == "__main__":
    main()
