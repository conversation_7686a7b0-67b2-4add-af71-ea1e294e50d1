import ctypes
import math
import os
import shutil
from ast import Index
from csv import excel
from importlib.metadata import metadata
from itertools import cycle
from logging import Logger
from tkinter.ttk import Style
import signal
import pandas as pd
from loguru import logger
import transplant
from autoDMCodeTest import genDMTestCode
import subprocess
from excelUtil import writeDMExcelTemplate,unzip_file

from slxUtils import changeGetCarIDFcn,genCarIDInfo

no_unit = ["-", "", "/", "[/]", "nan"]

def matlab_kill():
    for line in os.popen('tasklist'):
        if 'MATLAB.exe' in line:
            process_info=line.split()
            os.kill(int(process_info[1]),signal.SIGTERM)



if __name__ == '__main__':
    str_tmp = ""

    #车型标定表
    calibration_fileName = r"E:\03_项目相关\04_VSE项目\07-01_VSE2.0项目2.25.00SZHF-OBA车型DM模块\01_模型\VSE_DM标定量表.xlsx"
    #车型接口文件
    interface_fileName = r"E:\03_项目相关\04_VSE项目\07-01_VSE2.0项目2.25.00SZHF-OBA车型DM模块\01_模型\VSE_DM接口文件.xlsx"
    #车型软件版本号
    vse_version="vse2.25.00"
    #车型名称
    car_name="SZHF-OBA"
    #车型关键信息表
    carInfo_fileName=r"E:\03_项目相关\04_VSE项目\07-01_VSE2.0项目2.25.00SZHF-OBA车型DM模块\VSEAA2.0-SWE1-014_VSE2.0项目SZHF车型底盘电控关键信息V1.0.0.xlsx"
    #DM模型名
    DM_fileName="VSE_VehDataMngt.slx"

    #DM模型模板
    dm_slx_model_path=r"E:\03_项目相关\SVN\98_VSEAA2.0\01_Development\02_SWE_SoftwareEngineeringProcessGroup\03_SWE3_SoftwareDetailedDesignAndUnitConstruction\01_ASW\D3\V2.00.40-50\02_模型\01_VehDataMngt\2.00.41版本DM模块（D2、D3、OBA）"

    #DM代码测试环境
    dm_project_folder = r"D:\code\python_code\DMUtils\DM_Auto_Test"
    #DM测试报告输出路径
    output_test_filepath = r"E:\03_项目相关\04_VSE项目\07-01_VSE2.0项目2.25.00SZHF-OBA车型DM模块\测试结果.xlsx"
    #差异性分析报告模板
    DM_deference_report_template_path=r"D:\code\python_code\DMUtils\VSEAA2.0-SWE3-035_VSE2.0项目车型差异性分析报告V1.0.0.xlsx"

    #DM输出文件路径
    output_filePath = r"E:\03_项目相关\04_VSE项目\07-01_VSE2.0项目2.25.00SZHF-OBA车型DM模块\01_模型"


    if os.path.exists("VSE_VehDataMngt.slx"):
        os.remove("VSE_VehDataMngt.slx")
    if os.path.exists("VSE_VehDataMngt.sldd"):
        os.remove("VSE_VehDataMngt.sldd")
    if os.path.exists("VSE_ComnDD.sldd"):
        os.remove("VSE_ComnDD.sldd")

    shutil.copy(dm_slx_model_path+"\VSE_VehDataMngt.slx","VSE_VehDataMngt.slx")
    shutil.copy(dm_slx_model_path+"\VSE_VehDataMngt.sldd","VSE_VehDataMngt.sldd")
    shutil.copy(dm_slx_model_path+"\VSE_ComnDD.sldd","VSE_ComnDD.sldd")

    if not os.path.exists(output_filePath):
        os.makedirs(output_filePath)

    cali_signals = pd.read_excel(calibration_fileName, '参数')

    for index, row in cali_signals.iterrows():
        signalName = row.iloc[1]
        if isinstance(signalName,str) and "ca_VSE" in signalName:
            str_tmp += signalName + "=Simulink.Parameter;\n"
            str_tmp += signalName + ".CoderInfo.StorageClass='" + "Custom';\n"
            str_tmp += signalName + ".DataType='" + row.iloc[4] + "';\n"
            initalValue=row.iloc[7]
            str_tmp += signalName + ".Value=" + str(initalValue) + ";\n"
            str_tmp += signalName + ".Dimensions=" + str(row.iloc[13]) + ";\n"
            if row.iloc[11] == "Const":
                str_tmp += signalName + ".CoderInfo.CustomStorageClass='Const';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.HeaderFile='VSE_const';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.DefinitionFile='VSE_const';\n"
            if row.iloc[11] == "Volatile":
                str_tmp += signalName + ".CoderInfo.CustomStorageClass='Volatile';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.HeaderFile='VSE_Volatile';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.DefinitionFile='VSE_Volatile';\n"

    refer_signal= pd.read_excel(calibration_fileName, '查找表')
    for index, row in refer_signal.iterrows():
        signalName=row.iloc[1]
        if isinstance(signalName,str) and "ca_VSE_DM" in signalName:
            str_tmp += signalName + "=Simulink.Parameter;\n"
            str_tmp += signalName + ".CoderInfo.StorageClass='" + "Custom';\n"
            str_tmp += signalName + ".DataType='" + row.iloc[4] + "';\n"
            if row.iloc[11] == "Volatile":
                str_tmp += signalName + ".CoderInfo.CustomStorageClass='Volatile';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.HeaderFile='VSE_Volatile';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.DefinitionFile='VSE_Volatile';\n"
            if row.iloc[11] == "Const":
                str_tmp += signalName + ".CoderInfo.CustomStorageClass='Const';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.HeaderFile='VSE_const';\n"
                str_tmp += signalName + ".CoderInfo.CustomAttributes.DefinitionFile='VSE_const';\n"
            initialValue="".join(filter(lambda x : x not in ["\t","\n"],row.iloc[7]))
            str_tmp += signalName + ".Value=" + initialValue+ ";\n"
            str_tmp += signalName + ".Dimensions=" + str(row.iloc[13]) + ";\n"

    imput_signals = pd.read_excel(interface_fileName, '输入信号')
    for index, row in imput_signals.iterrows():
        signalName = row.iloc[1]
        if isinstance(signalName,str) and "VSE" in signalName:
            str_tmp += signalName + "=Simulink.Signal;\n"
            str_tmp += signalName + ".DataType='" + row.iloc[4] + "';\n"
            initalValue=row.iloc[7]
            if (not isinstance(initalValue,str) and not math.isnan(initalValue)) or (isinstance(initalValue, str) and "0x" not in initalValue):
                str_tmp += signalName + ".InitialValue='" + str(initalValue) + "';\n"
            str_tmp += signalName + ".CoderInfo.StorageClass='" +row.iloc[11]+ "';\n"
            str_tmp+=signalName + ".Dimensions=" + str(row.iloc[13]) + ";\n"

    output_signals=  pd.read_excel(interface_fileName, '输出信号')
    for index,row in output_signals.iterrows():
        signalName=row.iloc[1]
        if isinstance(signalName,str) and "VSE" in signalName:
            str_tmp += signalName + "=Simulink.Signal;\n"
            str_tmp += signalName + ".DataType='" + row.iloc[4] + "';\n"
            initalValue = row.iloc[7]
            if (not isinstance(initalValue, str) and not math.isnan(initalValue)) or (isinstance(initalValue, str) and "0x" not in initalValue):
                str_tmp += signalName + ".InitialValue='" + str(initalValue) + "';\n"
            str_tmp += signalName + ".CoderInfo.StorageClass='" + row.iloc[11] + "';\n"
            str_tmp += signalName + ".Dimensions=" + str(row.iloc[13]) + ";\n"

    with open("file.m", "w", encoding='utf-8') as file:
        file.write(str_tmp)
    if os.path.exists("slprj"):
        shutil.rmtree("slprj",ignore_errors=True)
    matlab = transplant.Matlab(executable=r"D:\Program Files\MATLAB\R2018b\bin\matlab.exe",jvm=False, desktop=False)
    matlab.generate("VSE_VehDataMngt.sldd","file.m")
    logger.debug("数据字典生成成功！")
    info = genCarIDInfo(carInfo_fileName)
    changeGetCarIDFcn(DM_fileName, info, vse_version)
    logger.debug("生成DM代码中...")
    matlab.slbuild("VSE_VehDataMngt")
    os.remove("file.m")
    logger.debug("DM代码生成完成！")

    copy_DM_slx=os.path.join(output_filePath,"VSE_VehDataMngt.slx")
    shutil.copy("VSE_VehDataMngt.slx",copy_DM_slx)
    os.remove("VSE_VehDataMngt.slx")

    copy_DM_sldd=os.path.join(output_filePath,"VSE_VehDataMngt.sldd")
    shutil.copy("VSE_VehDataMngt.sldd",copy_DM_sldd)
    os.remove("VSE_VehDataMngt.sldd")

    copy_Comm_sldd=os.path.join(output_filePath,"VSE_ComnDD.sldd")
    shutil.copy("VSE_ComnDD.sldd",copy_Comm_sldd)
    os.remove("VSE_ComnDD.sldd")

    copy_DM_code=os.path.join(output_filePath,"VSE_VehDataMngt_ert_rtw")
    if os.path.exists(copy_DM_code):
        shutil.rmtree(copy_DM_code)
    shutil.copytree("VSE_VehDataMngt_ert_rtw",copy_DM_code)
    shutil.rmtree("VSE_VehDataMngt_ert_rtw")
    matlab_kill()
    logger.debug("生成DM车型差异性分析报告...")
    dm_code_folder=os.path.join(dm_project_folder,"DM_Code")

    genDMTestCode(copy_DM_code,dm_code_folder)

    command = os.path.join(dm_project_folder, "AutoTest.sln")
    subprocess.run(f"msbuild.exe {command}", stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    exe_file_path = os.path.join(dm_project_folder, "x64\Debug\AutoTest.exe")
    subprocess.run([exe_file_path, carInfo_fileName, output_test_filepath])

    DM_deference_report=f"VSEAA2.0-SWE3-035_VSE2.0项目{car_name}车型差异性分析报告V1.0.0"
    output_DM_deference_report=os.path.join(output_filePath,DM_deference_report)
    shutil.copy(DM_deference_report_template_path,output_DM_deference_report)
    writeDMExcelTemplate(output_DM_deference_report,car_name,vse_version,os.path.basename(carInfo_fileName),output_test_filepath)
    logger.debug("DM车型差异性分析报告生成完成！")

    os._exit(1)

